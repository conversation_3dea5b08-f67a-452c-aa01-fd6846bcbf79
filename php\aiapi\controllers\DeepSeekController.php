<?php
/**
 * DeepSeek API控制器
 *
 * 🚨 架构边界规范：
 * ✅ 仅负责模拟DeepSeek平台的原生API行为和特征
 * ✅ 严格按照DeepSeek官方API文档验证参数和返回响应格式
 * ✅ 保持DeepSeek平台特有的特征：剧情生成和分镜脚本专家
 * ✅ 支持本地开发测试，不产生真实费用
 * ❌ 不负责环境切换逻辑（由AiServiceClient负责）
 *
 * 职责说明：
 * - 本控制器专注于DeepSeek平台特征的模拟实现
 * - 环境切换由php\api\app\Services\AiServiceClient.php处理
 * - 配置切换由php\api\config\ai.php控制
 *
 * 业务特长：剧情生成和分镜脚本专家
 * 支持功能：文本生成、对话完成、代码补全等
 */

class DeepSeekController
{
    private $logger;
    private $config;
    private $platform = 'deepseek';

    public function __construct()
    {
        global $aiapi_config;
        $this->logger = new Logger();
        $this->config = $aiapi_config['platforms'][$this->platform] ?? [];

        $this->logger->info("DeepSeek控制器初始化", [
            'platform' => $this->platform,
            'config_loaded' => !empty($this->config)
        ]);
    }
    
    /**
     * 对话完成接口
     * POST /deepseek/chat/completions
     */
    public function chatCompletions($params = [])
    {
        $startTime = microtime(true);

        try {
            $data = HttpHelper::getRequestBody();

            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'messages']);

            // 验证模型
            $this->validateModel($data['model']);

            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);

            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateDeepSeekErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }

            // 生成响应
            $response = $this->generateChatResponse($data);

            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'chat/completions',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );

            return $response;

        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'chat/completions', $e->getMessage(), $data ?? []);
            return $this->generateDeepSeekErrorResponse('INTERNAL_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 代码补全接口
     * POST /deepseek/completions
     */
    public function completions($params = [])
    {
        $startTime = microtime(true);
        
        try {
            $data = HttpHelper::getRequestBody();
            
            // 验证必需参数
            HttpHelper::validateRequiredParams($data, ['model', 'prompt']);
            
            // 验证模型
            $this->validateModel($data['model']);
            
            // 模拟网络延迟
            HttpHelper::simulateDelay($this->platform);
            
            // 检查成功率
            if (!HttpHelper::simulateSuccessRate($this->platform)) {
                return $this->generateDeepSeekErrorResponse('SERVICE_UNAVAILABLE', '服务暂时不可用，请稍后重试');
            }
            
            // 生成响应
            $response = $this->generateCompletionResponse($data);
            
            // 记录日志
            $duration = (microtime(true) - $startTime) * 1000;
            $this->logger->logApiRequest(
                $this->platform,
                'completions',
                'POST',
                $data,
                $response,
                round($duration, 2)
            );
            
            return $response;
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'completions', $e->getMessage(), $data ?? []);
            return $this->generateDeepSeekErrorResponse('INTERNAL_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 模型列表
     * GET /deepseek/models
     */
    public function listModels($params = [])
    {
        try {
            $models = [
                [
                    'id' => 'deepseek-chat',
                    'object' => 'model',
                    'created' => time(),
                    'owned_by' => 'deepseek'
                ],
                [
                    'id' => 'deepseek-coder',
                    'object' => 'model',
                    'created' => time(),
                    'owned_by' => 'deepseek'
                ],
                [
                    'id' => 'deepseek-reasoner',
                    'object' => 'model',
                    'created' => time(),
                    'owned_by' => 'deepseek'
                ]
            ];
            
            return [
                'object' => 'list',
                'data' => $models
            ];
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'models', $e->getMessage(), []);
            return $this->generateDeepSeekErrorResponse('INTERNAL_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 余额查询
     * GET /deepseek/user/balance
     */
    public function getBalance($params = [])
    {
        try {
            return [
                'balance_infos' => [
                    [
                        'currency' => 'CNY',
                        'total_balance' => '100.00',
                        'granted_balance' => '0.00',
                        'topped_up_balance' => '100.00'
                    ]
                ]
            ];
            
        } catch (Exception $e) {
            $this->logger->logApiError($this->platform, 'user/balance', $e->getMessage(), []);
            return $this->generateDeepSeekErrorResponse('INTERNAL_ERROR', $e->getMessage());
        }
    }
    
    /**
     * 生成对话响应
     */
    private function generateChatResponse($data)
    {
        $model = $data['model'];
        $messages = $data['messages'];
        
        // 生成响应内容
        $responseContent = $this->generateContentByContext($messages);
        
        // 计算token使用量
        $promptTokens = $this->estimateTokens($messages);
        $completionTokens = $this->estimateTokens($responseContent);
        $totalTokens = $promptTokens + $completionTokens;
        
        $response = [
            'id' => 'chatcmpl-' . uniqid(),
            'object' => 'chat.completion',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'index' => 0,
                    'message' => [
                        'role' => 'assistant',
                        'content' => $responseContent
                    ],
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $totalTokens
            ]
        ];
        
        // 如果是推理模型，添加推理过程
        if ($model === 'deepseek-reasoner') {
            $response['choices'][0]['reasoning_content'] = $this->generateReasoningProcess($messages);
        }
        
        return $response;
    }
    
    /**
     * 生成补全响应
     */
    private function generateCompletionResponse($data)
    {
        $model = $data['model'];
        $prompt = $data['prompt'];
        
        // 生成补全内容
        $completionText = $this->generateCodeCompletion($prompt);
        
        // 计算token使用量
        $promptTokens = $this->estimateTokens($prompt);
        $completionTokens = $this->estimateTokens($completionText);
        $totalTokens = $promptTokens + $completionTokens;
        
        return [
            'id' => 'cmpl-' . uniqid(),
            'object' => 'text_completion',
            'created' => time(),
            'model' => $model,
            'choices' => [
                [
                    'text' => $completionText,
                    'index' => 0,
                    'finish_reason' => 'stop'
                ]
            ],
            'usage' => [
                'prompt_tokens' => $promptTokens,
                'completion_tokens' => $completionTokens,
                'total_tokens' => $totalTokens
            ]
        ];
    }
    
    /**
     * 根据上下文生成内容
     */
    private function generateContentByContext($messages)
    {
        $lastMessage = end($messages);
        $userInput = $lastMessage['content'] ?? '';
        
        // 根据用户输入生成相应的内容
        if (strpos($userInput, '任务要求：基于`故事大纲`') !== false) {
            return $this->generateScriptContent('aistory');
        } 
        elseif (strpos($userInput, '任务要求：基于`自有故事`') !== false) {
            return $this->generateScriptContent('mystory');
        } 
        elseif (strpos($userInput, '任务要求：基于原始故事') !== false) {
            return $this->generateStoryContent($userInput);
        } 
        elseif (strpos($userInput, '任务要求：基于角色描述') !== false) {
            return $this->generateCharacterContent($userInput);
        } 
        elseif (strpos($userInput, '任务要求：基于风格描述') !== false) {
            return $this->generateStyleContent($userInput);
        } 
        elseif (strpos($userInput, '任务要求：基于分镜剧情') !== false && strpos($userInput, '分镜图片提示词') !== false) {
            return $this->generatePromptContent($userInput);
        } else {
            return $this->generateGeneralContent($userInput);
        }
    }
    
    /**
     * 生成故事内容
     */
    private function generateStoryContent($input)
    {
        $stories = [
            "在一个阳光明媚的夏日午后，海浪轻柔地拍打着金色的沙滩，微风带着海水的咸味轻抚过每个人的脸庞。远处的海鸥在蔚蓝天空中自由翱翔，叫声与海浪声交织成美妙的自然交响曲。这是关于友谊与成长的故事，主人公小明和朋友们在美丽海滩度过难忘夏天，一起建造沙堡、追逐海浪、在夕阳西下时分享梦想。他们发现了一个神秘的海螺，传说能实现愿望，但最终明白真正的宝藏是彼此间深厚的友谊和共同经历的美好时光，这个夏天成为他们青春记忆中最珍贵的片段。",
            "故事发生在一个充满魔法的古老森林深处，千年橡树在微风中轻摆，阳光透过茂密树叶洒下斑驳光影。在这神秘世界里，每片叶子都蕴含着远古魔力，每朵花都诉说着被遗忘的传说。年轻的魔法学徒艾莉娅踏入这片禁地，寻找能够拯救村庄的生命之泉。她遇到了会说话的智慧树精、调皮的光之精灵和守护森林的巨龙。经历重重考验后，艾莉娅发现真正的魔法不在于强大的咒语，而在于对自然的敬畏和内心的纯净。最终她不仅找到了生命之泉，更成为了森林与人类世界之间的桥梁，开启了和谐共存的新篇章。",
            "这是关于勇气和冒险的传奇故事。年轻探险家杰克带着祖父留下的神秘地图，踏上寻找传说中黄金城的危险旅程。他穿越了炽热的沙漠、攀登了险峻的雪山、探索了幽深的地下迷宫，沿途遇到了狡猾的盗贼、凶猛的野兽和古老的机关陷阱。在最艰难的时刻，他结识了智慧的老向导、勇敢的女战士和忠诚的伙伴们。经历无数生死考验后，杰克终于找到了黄金城，却发现真正的宝藏不是满屋的金银财宝，而是旅途中收获的珍贵友谊、坚韧意志和内心的成长。他选择与伙伴们分享财富，用这些宝藏帮助需要的人们。"
        ];
        
        return $stories[array_rand($stories)];
    }

    /**
     * 生成分镜脚本内容
     */
    private function generateScriptContent($key)
    {
        $scripts = [
// 根据提示智能扩写并且分镜：一次满足多条件
            'aistory' => '
{
  "故事标题": "程韵复仇",
  "故事简概": "家道中落的程韵，为复仇刻意接近仇人之弟陆霆川，并利用其信任搜集证据，最终在家族宴会上揭露仇人陆震的罪行，使其身败名裂。",
  "场景1": {
    "场景名称": "废弃的程家别墅/高级酒会",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "雷雨/晴",
    "场景提示词": "哥特式风格的废弃别墅，蛛网遍布，尘土飞扬，窗外电闪雷鸣。切换到灯火辉煌、衣香鬓影的现代豪华酒会现场。",
    "分镜": [
      {
        "分镜序号": 1,
        "出境角色": "程韵",
        "字幕": "曾经的商业帝国一夜倾塌，只剩下这座空洞的牢笼。",
        "分镜提示词": "程韵独自站在空旷破败的别墅大厅中央，一道闪电照亮她苍白但决绝的脸庞，手中紧握着一份宣告家族破产的文件。"
      },
      {
        "分镜序号": 2,
        "出境角色": "程韵",
        "字幕": "而这一切的始作俑者——陆震，必须付出代价。",
        "分镜提示词": "特写镜头，程韵的眼中倒映着手机屏幕上陆震志得意满的新闻照片，眼神中充满恨意。"
      },
      {
        "分镜序号": 3,
        "出境角色": "程韵",
        "字幕": "要击垮他，我需要一把最锋利的刀……他的亲弟弟，陆霆川。",
        "分镜提示词": "镜头切换，程韵看着屏幕上陆震身旁的另一个男人——陆霆川，他英俊冷漠，气质不凡。程韵的嘴角浮现一抹复杂的微笑。"
      },
      {
        "分镜序号": 4,
        "出境角色": "程韵,陆霆川",
        "字幕": "（优雅的音乐）",
        "分镜提示词": "场景变换为豪华酒会，程韵身着一袭红色长裙，魅力四射，故意将红酒洒在路过的陆霆川身上。"
      },
      {
        "分镜序号": 5,
        "出境角色": "程韵,陆霆川",
        "字幕": "我的复仇，从这一刻开始。",
        "分镜提示词": "陆霆川低头看着自己身上的酒渍，再抬头看向程韵，眼神从错愕变为探究。程韵则递上一方手帕，眼神无辜又充满魅惑。"
      }
    ]
  },
  "场景2": {
    "场景名称": "陆氏集团办公室",
    "空间": "室内",
    "时间": "白天/深夜",
    "天气": "晴",
    "场景提示词": "现代、极简、奢华的顶层办公室，巨大的落地窗外是繁华的城市天际线。深夜则只有一盏台灯亮着，气氛紧张。",
    "分镜": [
      {
        "分镜序号": 6,
        "出境角色": "程韵,陆霆川",
        "字幕": "凭借才华，我顺利进入陆霆川的公司，成为他最信任的助理。",
        "分镜提示词": "明亮的办公室内，程韵正向陆霆川汇报一份完美的项目方案，陆霆川点头表示赞许，眼神中流露出欣赏。"
      },
      {
        "分镜序号": 7,
        "出境角色": "程韵",
        "字幕": "白天，我是他无懈可击的伙伴。",
        "分镜提示词": "快速剪辑：程韵在会议上侃侃而谈、为陆霆川整理行程、在商业谈判中为他解围的多个画面，展现她的专业能力。"
      },
      {
        "分镜序号": 8,
        "出境角色": "程韵",
        "字幕": "夜晚，我是窃取秘密的影子。",
        "分镜提示词": "深夜的办公室，只有电脑屏幕的光照在程韵脸上。她神情紧张地将一个U盘插入陆霆川的电脑，复制着加密文件。"
      },
      {
        "分镜序号": 9,
        "出境角色": "程韵,陆霆川",
        "字幕": "（心跳声）",
        "分镜提示词": "办公室的门突然被推开，陆霆川走了进来。程韵迅速关掉窗口，假装在加班，心脏几乎跳出胸膛。"
      },
      {
        "分镜序号": 10,
        "出境角色": "程韵",
        "字幕": "终于，我拿到了陆震全部的犯罪证据。",
        "分镜提示词": "特写，程韵看着U盘中显示的“传输完成”字样，悄悄松了一口气，眼中闪过一丝胜利的寒光。"
      }
    ]
  },
  "场景3": {
    "场景名称": "陆家家族宴会厅",
    "空间": "室内",
    "时间": "夜晚",
    "天气": "晴",
    "场景提示词": "金碧辉煌、布置奢华的宴会大厅，宾客云集，气氛热烈。巨大的水晶吊灯，长桌上摆满精致的餐点。",
    "分镜": [
      {
        "分镜序号": 11,
        "出境角色": "陆震,陆霆川,程韵",
        "字幕": "陆家的年度晚宴，是陆震最风光的时刻，也将是他的末日。",
        "分镜提示词": "陆震站在台上，手持香槟，意气风发地向来宾致辞。台下的陆霆川面带微笑，身边的程韵则神色平静，暗中握紧了手机。"
      },
      {
        "分镜序号": 12,
        "出境角色": "程韵",
        "字幕": "“陆总，在庆祝之前，不如先看一份献给您的‘大礼’？”",
        "分镜提示词": "程韵突然开口，声音清亮，打断了陆震的讲话。全场的目光瞬间聚焦在她身上。"
      },
      {
        "分镜序号": 13,
        "出境角色": "陆震,众宾客",
        "字幕": "（宾客哗然）",
        "分镜提示词": "陆震身后的大屏幕突然亮起，开始播放他挪用公款、进行内幕交易的转账记录和密谈录音，宾客们一片哗然。"
      },
      {
        "分镜序号": 14,
        "出境角色": "陆震",
        "字幕": "“不！这不是真的！是你！程韵！”",
        "分镜提示词": "特写镜头，陆震的脸因震惊和愤怒而扭曲，他不敢置信地指着台下的程韵。"
      },
      {
        "分镜序号": 15,
        "出境角色": "程韵,陆霆川",
        "字幕": "“游戏结束了。”",
        "分镜提示词": "程韵迎着他的目光，表情冰冷而平静。她身旁的陆霆川，脸上的微笑早已消失，转头看着她，眼神充满了震惊、不解和一丝痛楚。"
      },
      {
        "分镜序号": 16,
        "出境角色": "程韵",
        "字幕": "（闪回程家别墅的废墟）爸爸，妈妈，我为你们报仇了。",
        "分镜提示词": "最终镜头聚焦于程韵，她的眼中一滴泪滑落，但嘴角却带着复仇成功的释然微笑，背景虚化为骚乱的宴会现场。"
      }
    ]
  }
}
            ',
// 根据自有故事分镜：一次满足多条件
            'mystory' => '
{
  "故事标题": "贫穷高中生陆星",
  "故事简概": "贫穷高中生陆星被富豪管家支付一百万，要求他停止保护富豪女儿魏青鱼，并回忆起当初被雇佣的缘由。",
  "分镜1": {
    "场景名称": "咖啡厅包间",
    "空间": "室内",
    "时间": "白天",
    "天气": "晴",
    "出境角色": "陆星,管家",
    "字幕": "“这里有一百万，离开青鱼小姐。”“我们的交易，到此结束。”咚！一个旅行袋被随意丢在桌子上。",
    "分镜提示词": "一位西装革履的管家将一个黑色旅行袋丢在桌子上，对面的少年陆星面无表情地看着他，光线从窗户照入，氛围紧张。"
  },
  "分镜2": {
    "场景名称": "咖啡厅包间",
    "空间": "室内",
    "时间": "白天",
    "天气": "晴",
    "出境角色": "陆星",
    "字幕": "陆星拉开背包，一叠叠红色钞票争先恐后掉在他身上！这个世界上最伟大的颜色——红色！",
    "分镜提示词": "特写镜头，少年陆星拉开旅行袋拉链，无数叠崭新的人民币百元大钞从包里涌出，散落在桌上和他的身上，他眼神发光。"
  },
  "分镜3": {
    "场景名称": "咖啡厅包间",
    "空间": "室内",
    "时间": "白天",
    "天气": "晴",
    "出境角色": "陆星,管家",
    "字幕": "管家恨铁不成钢的说。“你才十八岁，为什么要沦落到这一行？！”“好赌的爹，酗酒的妈，生病的妹妹和破碎的我。”陆星专业的从包里掏出来了个验钞机，一边数钱一边敷衍说道。“满嘴顺口溜，你要考研啊？”",
    "分镜提示词": "管家痛心疾首地指着陆星，而陆星则一脸淡定地拿出验钞机，开始清点桌上的钞票，对管家的话不以为然。"
  },
  "分镜4": {
    "场景名称": "咖啡厅包间",
    "空间": "室内",
    "时间": "白天",
    "天气": "晴",
    "出境角色": "陆星,管家",
    "字幕": "不对！陆星数钱数一半，突然想起来管家话里的重点，于是问道。“你的意思是......”“我以后就不用颠颠的跟在魏青鱼屁股后面照顾她了是吧？”管家气得脸涨红，结结巴巴的说。“粗鄙！粗鄙！”",
    "分镜提示词": "陆星突然停下数钱的动作，抬头惊喜地看着管家，而管家被他粗俗的用词气得满脸通红，手指着他，说不出话。"
  },
  "分镜5": {
    "场景名称": "咖啡厅包间",
    "空间": "室内",
    "时间": "白天",
    "天气": "晴",
    "出境角色": "陆星,管家",
    "字幕": "陆星没搭理管家，低头继续数钱。管家平复好情绪，解释道。“快毕业了，老板打算加强对青鱼小姐的保护，所以不再需要你了。”“这一百万是补偿你的。”",
    "分镜提示词": "陆星再次低下头，专注于数钱，脸上带着一丝窃喜。对面的管家整理了一下衣领，恢复了冷静，用公事公办的语气向他解释。"
  },
  "分镜6": {
    "场景名称": "学校",
    "空间": "室外",
    "时间": "两年半之前",
    "天气": "晴",
    "出境角色": "魏青鱼",
    "字幕": "（回忆）两年半之前，魏青鱼进入学校。她长得清冷精致，但性格冷漠寡言。大把的人为她的美貌前仆后继，但都倒在她的冷眼之下。得不到就毁掉的态度，一时之间，谣言四起。",
    "分镜提示词": "【回忆风格】美丽的少女魏青鱼在校园里独自行走，表情冷漠，周围的同学或爱慕或嫉妒地看着她，背景中浮现出一些文字气泡代表谣言。"
  },
  "分镜7": {
    "场景名称": "学校",
    "空间": "室外",
    "时间": "两年半之前",
    "天气": "晴",
    "出境角色": "无",
    "字幕": "（回忆）这件事被魏老爹知道后直接暴怒！他有钞能力。一怒之下，给学校捐了一栋楼。谣言瞬间消失！",
    "分镜提示词": "【回忆风格】一张报纸的头条，标题是“富豪魏XX为爱女向XX学校捐赠教学楼”，背景是崭新的教学楼和烟消云散的谣言文字。"
  },
  "分镜8": {
    "场景名称": "魏家书房",
    "空间": "室内",
    "时间": "两年半之前",
    "天气": "晴",
    "出境角色": "魏老爹,陆星",
    "字幕": "（回忆）魏老爹还是担心女儿，纠结之间，陆星进入了他的视线。练过武术，家境贫寒，学习不错，还是魏青鱼同班同学。这要是雇佣来保护魏青鱼......",
    "分镜提示词": "【回忆风格】豪华书房内，一个威严的男人（魏老爹）看着一份文件，文件上有陆星的照片和资料，上面写着“武术特长”、“家境贫寒”、“成绩优异”等字样，他露出思索的神情。"
  }
}',
        ];

        return $scripts[$key];
    }

    /**
     * 生成角色内容
     * 返回符合新格式要求的角色数据
     */
    private function generateCharacterContent($input)
    {
        $characters = [
            // 人类角色 - 各年龄阶段
            [
                "角色名称" => "一个可爱的小男孩",
                "提示词" => "一个可爱的小男孩，圆圆的脸蛋，大大的眼睛，穿着蓝色小背心和短裤，手里拿着玩具车，笑容天真无邪",
                "类型" => "人类",
                "性别" => "男",
                "年龄阶段" => "婴幼儿(0-6岁)",
                "TAG" => ["可爱", "天真", "童年", "玩具", "活泼"]
            ],
            [
                "角色名称" => "一个可爱的小女孩",
                "提示词" => "一个可爱的小女孩，金色卷发，大大的蓝色眼睛，穿着粉色连衣裙，手里抱着毛绒玩具，笑容天真烂漫，充满童真",
                "类型" => "人类",
                "性别" => "女",
                "年龄阶段" => "儿童(6-12岁)",
                "TAG" => ["可爱", "天真", "童真", "金发", "甜美"]
            ],
            [
                "角色名称" => "一个勇敢善良的少年",
                "提示词" => "一个勇敢善良的少年，黑色短发，明亮的眼睛，阳光的笑容，身穿校服，背着书包，充满青春活力和冒险精神",
                "类型" => "人类",
                "性别" => "男",
                "年龄阶段" => "少年(12-18岁)",
                "TAG" => ["勇敢", "善良", "阳光", "青春", "冒险"]
            ],
            [
                "角色名称" => "一个聪明细心的少女",
                "提示词" => "一个聪明细心的少女，长发飘飘，戴着圆框眼镜，温文尔雅的气质，身穿学院风服装，手持书本，散发着书香门第的优雅气息",
                "类型" => "人类",
                "性别" => "女",
                "年龄阶段" => "少年(12-18岁)",
                "TAG" => ["聪明", "优雅", "学院风", "书香", "温文尔雅"]
            ],
            [
                "角色名称" => "一位英俊的年轻男子",
                "提示词" => "一位英俊的年轻男子，棕色头发，深邃的眼神，身穿现代商务装，手持公文包，展现出专业和自信的气质",
                "类型" => "人类",
                "性别" => "男",
                "年龄阶段" => "青年(18-40岁)",
                "TAG" => ["英俊", "专业", "自信", "商务", "成熟"]
            ],
            [
                "角色名称" => "一位神秘的魔法师",
                "提示词" => "一位神秘的魔法师，银色长发飘逸，紫色眼眸深邃，身穿星空图案的长袍，手持法杖，周围环绕着魔法光芒和森林精灵",
                "类型" => "人类",
                "性别" => "女",
                "年龄阶段" => "青年(18-40岁)",
                "TAG" => ["神秘", "魔法", "奇幻", "优雅", "魅力"]
            ],
            [
                "角色名称" => "一位成熟稳重的中年男性",
                "提示词" => "一位成熟稳重的中年男性，略显花白的头发，温和的笑容，身穿深色西装，手持眼镜，展现出智慧和经验的气质",
                "类型" => "人类",
                "性别" => "男",
                "年龄阶段" => "中年(40-65岁)",
                "TAG" => ["成熟", "稳重", "智慧", "经验", "温和"]
            ],
            [
                "角色名称" => "一位慈祥的老奶奶",
                "提示词" => "一位慈祥的老奶奶，银白色的头发盘成发髻，温暖的笑容，身穿传统的深色长裙，手里拿着编织针，散发着家的温暖",
                "类型" => "人类",
                "性别" => "女",
                "年龄阶段" => "老年(65岁以上)",
                "TAG" => ["慈祥", "温暖", "传统", "家庭", "关爱"]
            ],

            // 拟人动物角色
            [
                "角色名称" => "一只聪明的橙色狐狸",
                "提示词" => "一只聪明的橙色狐狸，穿着蓝色背心和绿色裤子，戴着红色帽子，表情机灵可爱，站立行走，具有人类的智慧和表情",
                "类型" => "拟人动物",
                "性别" => "男",
                "年龄阶段" => "青年(18-40岁)",
                "TAG" => ["聪明", "机灵", "可爱", "智慧", "活泼"]
            ],
            [
                "角色名称" => "一只优雅的白色兔子",
                "提示词" => "一只优雅的白色兔子，穿着粉色蕾丝裙子，戴着小花帽，手持胡萝卜，表情温柔可爱，站立姿态优雅迷人",
                "类型" => "拟人动物",
                "性别" => "女",
                "年龄阶段" => "少年(12-18岁)",
                "TAG" => ["优雅", "温柔", "可爱", "迷人", "纯真"]
            ],
            [
                "角色名称" => "一只威严的狮子",
                "提示词" => "一只威严的狮子，穿着金色的王袍，戴着王冠，手持权杖，表情庄严威武，展现出王者的气质和威严",
                "类型" => "拟人动物",
                "性别" => "男",
                "年龄阶段" => "中年(40-65岁)",
                "TAG" => ["威严", "王者", "庄严", "威武", "权威"]
            ],

            // 原生动物角色
            [
                "角色名称" => "一只优雅的白色天鹅",
                "提示词" => "一只优雅的白色天鹅，羽毛洁白如雪，脖颈修长优美，在清澈的湖面上游泳，周围有荷花和水草，展现自然的美丽和宁静",
                "类型" => "原生动物",
                "性别" => "女",
                "年龄阶段" => "青年(18-40岁)",
                "TAG" => ["优雅", "美丽", "宁静", "纯洁", "自然"]
            ],
            [
                "角色名称" => "一只雄壮的金色雄鹰",
                "提示词" => "一只雄壮的金色雄鹰，展开巨大的翅膀，锐利的眼神，站在高山之巅，背景是蓝天白云，展现出自由和力量的象征",
                "类型" => "原生动物",
                "性别" => "男",
                "年龄阶段" => "青年(18-40岁)",
                "TAG" => ["雄壮", "自由", "力量", "锐利", "高贵"]
            ],
            [
                "角色名称" => "一只可爱的小熊猫",
                "提示词" => "一只可爱的小熊猫，黑白相间的毛色，圆圆的眼睛，正在竹林中玩耍，周围是绿色的竹子，表情天真可爱",
                "类型" => "原生动物",
                "性别" => "女",
                "年龄阶段" => "儿童(6-12岁)",
                "TAG" => ["可爱", "天真", "玩耍", "纯真", "萌萌哒"]
            ]
        ];

        $selectedCharacter = $characters[array_rand($characters)];
        return json_encode($selectedCharacter, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 生成风格内容
     * 返回符合新格式要求的风格数据
     */
    private function generateStyleContent($input)
    {
        $styles = [
            [
                "风格名称" => "现代都市剧",
                "风格描述" => "现代简约风格，以冷色调为主，展现都市生活的快节奏和现代感。通过强烈的明暗对比营造戏剧张力，配合现代电子音乐增强节奏感。",
                "提示词" => "现代简约风格，冷色调为主，多用特写和中景镜头，节奏紧凑，蓝灰色调突出都市冷峻感，强烈明暗对比营造戏剧张力，现代电子音乐背景，展现商业竞争和职场斗争的紧张氛围",
                "TAG" => ["现代", "都市", "冷色调", "商务", "紧张"]
            ],
            [
                "风格名称" => "古装仙侠",
                "风格描述" => "唯美飘逸的古典风格，色彩饱和度高，大量运用航拍和远景展现山水之美。以青绿色调为主，金色点缀，营造仙境般的超脱氛围。",
                "提示词" => "唯美飘逸古典风格，色彩饱和度高，大量航拍和远景展现山水之美，青绿色调金色点缀，柔和自然光营造仙境氛围，古典民乐空灵悠远，展现修仙历练和江湖恩怨的神秘意境",
                "TAG" => ["古装", "仙侠", "唯美", "飘逸", "神秘"]
            ],
            [
                "风格名称" => "温馨治愈",
                "风格描述" => "温暖柔和的视觉风格，画面干净清新，节奏舒缓。以暖色调为主，避免强烈对比，配合轻柔的钢琴曲营造温暖人心的氛围。",
                "提示词" => "温暖柔和视觉风格，画面干净清新，平稳推拉摇移镜头，节奏舒缓，暖色调为主包括粉色橙色米色，柔和自然光避免强烈对比，轻柔钢琴曲温暖人心，展现家庭温情和友情治愈的正能量",
                "TAG" => ["温馨", "治愈", "暖色调", "家庭", "正能量"]
            ],
            [
                "风格名称" => "悬疑惊悚",
                "风格描述" => "阴暗压抑的视觉风格，构图不对称，多用低角度仰拍营造压迫感。以黑白灰为主色调，红色作为点缀，大量阴影增强神秘感。",
                "提示词" => "阴暗压抑视觉风格，构图不对称，低角度仰拍营造压迫感，黑白灰为主红色点缀，强烈明暗对比大量阴影，低沉背景音突然音效，展现推理破案和心理博弈的紧张不安氛围",
                "TAG" => ["悬疑", "惊悚", "阴暗", "压抑", "神秘"]
            ]
        ];

        $selectedStyle = $styles[array_rand($styles)];
        return json_encode($selectedStyle, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 生成分镜图片提示词内容
     */
    private function generatePromptContent($input)
    {
        $prompts = [
            "少年陆星双手紧握旅行袋拉链，用力拉开的瞬间，无数叠崭新的红色百元钞票如瀑布般从袋中涌出",
            "钞票如雨点般散落在桌面和少年身上，陆星眼中闪烁着兴奋的光芒，被金钱包围的震撼表情清晰可见",
            "咖啡厅包间内，旅行袋完全敞开，钞票在空中飞舞，少年陆星目瞪口呆地凝视着这意外的财富奇迹"
        ];

        return $prompts[array_rand($prompts)];
    }

    /**
     * 生成通用内容
     */
    private function generateGeneralContent($input)
    {
        $responses = [
            "感谢您的提问。作为DeepSeek AI助手，我很乐意为您提供帮助。基于您的输入，我理解您希望获得相关的信息和建议。",
            "这是一个很有趣的话题。让我为您详细分析一下相关的要点和可能的解决方案。",
            "根据您的描述，我认为这个问题可以从多个角度来考虑。让我为您提供一些思路和建议。"
        ];

        return $responses[array_rand($responses)] . "\n\n" . $this->generateDetailedResponse($input);
    }

    /**
     * 生成详细响应
     */
    private function generateDetailedResponse($input)
    {
        $details = [
            "首先，我们需要明确目标和需求。然后制定合理的计划，分步骤实施。在执行过程中要注意细节，及时调整策略。最后进行总结和反思，为下次提供经验。",
            "这个问题涉及多个方面的考虑。我建议从以下几个维度来分析：技术可行性、资源配置、时间安排、风险评估等。通过综合分析，我们可以找到最优的解决方案。",
            "让我为您提供一个系统性的分析框架。我们可以将问题分解为几个关键要素，然后逐一分析每个要素的特点和相互关系，最终形成完整的解决方案。"
        ];

        return $details[array_rand($details)];
    }

    /**
     * 生成推理过程
     */
    private function generateReasoningProcess($messages)
    {
        return "让我仔细分析这个问题...\n\n首先，我需要理解用户的具体需求和上下文。从对话历史来看，用户希望获得相关的帮助和建议。\n\n其次，我会考虑多种可能的解决方案，权衡各自的优缺点。\n\n最后，我会选择最适合的方案，并提供详细的说明和建议。";
    }

    /**
     * 生成代码补全
     */
    private function generateCodeCompletion($prompt)
    {
        // 简单的代码补全逻辑
        if (strpos($prompt, 'function') !== false) {
            return "{\n    // 函数实现\n    return result;\n}";
        } elseif (strpos($prompt, 'class') !== false) {
            return "{\n    constructor() {\n        // 构造函数\n    }\n    \n    method() {\n        // 方法实现\n    }\n}";
        } else {
            return "// 代码补全\nconsole.log('Hello, World!');";
        }
    }

    /**
     * 估算token数量
     */
    private function estimateTokens($text)
    {
        if (is_array($text)) {
            $totalText = '';
            foreach ($text as $message) {
                $totalText .= $message['content'] ?? '';
            }
            $text = $totalText;
        }

        // 简单估算：中文按字符数，英文按单词数
        $chineseChars = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text);
        $englishWords = str_word_count(preg_replace('/[\x{4e00}-\x{9fff}]/u', '', $text));

        return $chineseChars + $englishWords;
    }

    /**
     * 验证模型
     */
    private function validateModel($model)
    {
        $validModels = ['deepseek-chat', 'deepseek-coder', 'deepseek-reasoner'];

        if (!in_array($model, $validModels)) {
            throw new Exception("不支持的模型: {$model}");
        }
    }

    /**
     * 生成DeepSeek错误响应
     */
    private function generateDeepSeekErrorResponse($errorCode, $message)
    {
        $errorMap = [
            'SERVICE_UNAVAILABLE' => ['code' => 503, 'type' => 'service_unavailable'],
            'INTERNAL_ERROR' => ['code' => 500, 'type' => 'internal_server_error'],
            'INVALID_REQUEST' => ['code' => 400, 'type' => 'invalid_request_error'],
            'UNAUTHORIZED' => ['code' => 401, 'type' => 'authentication_error'],
            'RATE_LIMIT' => ['code' => 429, 'type' => 'rate_limit_exceeded']
        ];

        $error = $errorMap[$errorCode] ?? $errorMap['INTERNAL_ERROR'];

        http_response_code($error['code']);

        return [
            'error' => [
                'message' => $message,
                'type' => $error['type'],
                'code' => $errorCode
            ]
        ];
    }
}
