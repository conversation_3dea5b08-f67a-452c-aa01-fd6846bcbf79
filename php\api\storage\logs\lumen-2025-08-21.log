[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":8,"session_id":null} 
[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":7,"session_id":null} 
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"mystory","status":"allowed"} 
[2025-08-21 19:27:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"mystory","mapped_task_type":"text_generation_mystory","user_id":14,"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_mystory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_8iD7o1aMGjp6u8RYWjpsgUgdhgrbrGVB","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:22:34+08:00"},{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"}]} 
[2025-08-21 19:27:17] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","expires_at":"2025-08-21T19:32:17+08:00"} 
[2025-08-21 19:27:33] production.INFO: WebSocket心跳检查 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"last_ping_at":"2025-08-21T19:27:17+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:27:33] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"fd":1,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"},{"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:27:33+08:00"}]} 
[2025-08-21 19:28:19] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":4,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_mystory","text_generation_mystory"],"to_business_type":"text_generation_aistory","disconnected_sessions_count":2} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","expires_at":"2025-08-21T19:33:19+08:00"} 
[2025-08-21 19:28:30] production.INFO: WebSocket心跳检查 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"last_ping_at":"2025-08-21T19:28:19+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:28:30] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"fd":2,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:41] production.INFO: User offline {"user_id":14} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:53] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接认证成功 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","expires_at":"2025-08-21T19:33:53+08:00"} 
[2025-08-21 19:29:06] production.INFO: WebSocket心跳检查 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"last_ping_at":"2025-08-21T19:28:53+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:29:06] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"fd":3,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":257,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":257,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755775753.990739,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:14] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":13.63,"total_stack_cleared":true} 
[2025-08-21 19:29:14] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":0,"timestamp":"2025-08-21T19:29:14+08:00"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptcharacter","status":"allowed"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptcharacter","mapped_task_type":"text_prompt_character","user_id":14,"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_character","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:29:06+08:00"}]} 
[2025-08-21 19:29:42] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_character","frontend_business_type":"promptcharacter","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","old_business_type":"text_generation_aistory","new_business_type":"text_prompt_character","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_aistory"],"to_business_type":"text_prompt_character","disconnected_sessions_count":1} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","expires_at":"2025-08-21T19:34:42+08:00"} 
[2025-08-21 19:30:01] production.INFO: WebSocket心跳检查 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"last_ping_at":"2025-08-21T19:29:42+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:30:01] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"fd":4,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_character"]} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-21T19:30:01+08:00"}]} 
[2025-08-21 19:30:31] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","old_business_type":"text_prompt_character","new_business_type":"text_prompt_style","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_prompt_character"],"to_business_type":"text_prompt_style","disconnected_sessions_count":1} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接认证成功 {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","expires_at":"2025-08-21T19:35:31+08:00"} 
[2025-08-21 19:30:45] production.INFO: WebSocket心跳检查 {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"last_ping_at":"2025-08-21T19:30:31+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:30:45] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"fd":5,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:31:14] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":257,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:31:14] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:31:14] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":257,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755775874.025119,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:31:14] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":7.43,"total_stack_cleared":true} 
[2025-08-21 19:31:14] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":0,"timestamp":"2025-08-21T19:31:14+08:00"} 
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:33:03] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:33:03] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:33:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_style"]} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr"} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-21T19:30:45+08:00"}]} 
[2025-08-21 19:33:19] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","old_business_type":"text_prompt_style","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":2,"reason":"业务类型切换"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_prompt_style"],"to_business_type":"text_generation_aistory","disconnected_sessions_count":1} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","expires_at":"2025-08-21T19:38:19+08:00"} 
[2025-08-21 19:33:40] production.INFO: WebSocket会话重连 {"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:35:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:35:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:35:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776103.781519,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:35:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":76.88,"total_stack_cleared":true} 
[2025-08-21 19:35:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:35:03+08:00"} 
[2025-08-21 19:36:41] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755776201_PF87JpSQ","user_id":14,"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","business_type":"text_generation_aistory"} 
[2025-08-21 19:37:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:37:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:37:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776223.80443,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:37:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":39.03,"total_stack_cleared":true} 
[2025-08-21 19:37:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:37:03+08:00"} 
[2025-08-21 19:39:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:39:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:39:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776343.80206,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:39:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":60.28,"total_stack_cleared":true} 
[2025-08-21 19:39:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:39:03+08:00"} 
[2025-08-21 19:41:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:41:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:41:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776463.795291,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:41:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":130.7,"total_stack_cleared":true} 
[2025-08-21 19:41:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:41:03+08:00"} 
[2025-08-21 19:43:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:43:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:43:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776583.776138,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:43:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":106.48,"total_stack_cleared":true} 
[2025-08-21 19:43:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:43:03+08:00"} 
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:44:53] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:44:53] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:44:59] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:44:59] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF"} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:33:40+08:00"}]} 
[2025-08-21 19:44:59] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","expires_at":"2025-08-21T19:49:59+08:00"} 
[2025-08-21 19:45:15] production.INFO: WebSocket会话重连 {"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:46:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:46:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:46:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776813.562523,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:46:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":93.94,"total_stack_cleared":true} 
[2025-08-21 19:46:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:46:53+08:00"} 
[2025-08-21 19:48:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:48:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:48:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776933.540208,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:48:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":122.57,"total_stack_cleared":true} 
[2025-08-21 19:48:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:48:53+08:00"} 
[2025-08-21 19:50:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:50:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:50:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777053.529765,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:50:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":35.88,"total_stack_cleared":true} 
[2025-08-21 19:50:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:50:53+08:00"} 
[2025-08-21 19:52:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:52:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:52:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777173.532086,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:52:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":79.48,"total_stack_cleared":true} 
[2025-08-21 19:52:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:52:53+08:00"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:54:07] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:45:15+08:00"}]} 
[2025-08-21 19:54:07] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","expires_at":"2025-08-21T19:59:07+08:00"} 
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:54:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:54:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:54:27] production.INFO: WebSocket会话重连 {"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:56:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":658,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:56:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:56:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":658,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777373.287799,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:56:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":59.56,"total_stack_cleared":true} 
[2025-08-21 19:56:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:56:13+08:00"} 
[2025-08-21 19:58:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":658,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:58:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:58:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":658,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777493.277529,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:58:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":53.17,"total_stack_cleared":true} 
[2025-08-21 19:58:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:58:13+08:00"} 
[2025-08-21 19:58:59] production.INFO: User offline {"user_id":14} 
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 20:04:45] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 20:04:45] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 20:04:45] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 20:04:45] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj"} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 20:04:45] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接认证成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","expires_at":"2025-08-21T20:09:45+08:00"} 
[2025-08-21 20:05:01] production.INFO: WebSocket会话重连 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 20:06:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:06:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:06:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778005.813585,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:06:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":62.09,"total_stack_cleared":true} 
[2025-08-21 20:06:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:06:45+08:00"} 
[2025-08-21 20:08:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:08:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:08:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778125.816724,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:08:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":105.49,"total_stack_cleared":true} 
[2025-08-21 20:08:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:08:45+08:00"} 
[2025-08-21 20:10:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:10:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:10:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778245.818035,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:10:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":80.1,"total_stack_cleared":true} 
[2025-08-21 20:10:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:10:45+08:00"} 
[2025-08-21 20:12:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:12:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:12:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778365.8151,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:12:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":44.93,"total_stack_cleared":true} 
[2025-08-21 20:12:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:12:45+08:00"} 
[2025-08-21 20:14:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:14:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:14:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778485.79989,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:14:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":98.78,"total_stack_cleared":true} 
[2025-08-21 20:14:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:14:45+08:00"} 
[2025-08-21 20:16:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:16:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:16:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778605.823111,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:16:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":51.91,"total_stack_cleared":true} 
[2025-08-21 20:16:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:16:45+08:00"} 
[2025-08-21 20:18:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:18:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:18:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778725.818995,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:18:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":124.69,"total_stack_cleared":true} 
[2025-08-21 20:18:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:18:45+08:00"} 
[2025-08-21 20:20:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:20:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:20:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778845.818846,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:20:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":66.82,"total_stack_cleared":true} 
[2025-08-21 20:20:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:20:45+08:00"} 
[2025-08-21 20:22:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:22:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:22:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778965.819475,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:22:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":122.89,"total_stack_cleared":true} 
[2025-08-21 20:22:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:22:45+08:00"} 
[2025-08-21 20:24:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:24:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:24:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779085.821096,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:24:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":63.66,"total_stack_cleared":true} 
[2025-08-21 20:24:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:24:45+08:00"} 
[2025-08-21 20:26:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:26:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:26:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779205.819033,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:26:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":86.56,"total_stack_cleared":true} 
[2025-08-21 20:26:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:26:45+08:00"} 
[2025-08-21 20:28:37] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","business_type":"text_generation_aistory"} 
[2025-08-21 20:28:37] production.INFO: 项目验证成功 {"project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 20:28:37] production.INFO: 项目验证成功 {"business_type":"text_generation_aistory","project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 20:28:37] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4077,"current_balance":"9708.13","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-21 20:28:37] production.DEBUG: TransactionManager::begin 调用 {"context":"PointsService.freezePoints","current_level_before":0,"stack_size_before":0,"process_id":16696,"memory_usage":16777216,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:37] production.DEBUG: Lumen事务开始 {"context":"PointsService.freezePoints","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:28:37] production.DEBUG: TransactionManager::commit 调用 {"context":"PointsService.freezePoints","current_level_before":1,"stack_size_before":1,"process_id":16696,"memory_usage":18874368,"transaction_stack_before":[{"context":"PointsService.freezePoints","level":1,"timestamp":1755779317.042658,"memory_usage":16777216}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":159,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:37] production.DEBUG: 事务提交 {"context":"PointsService.freezePoints","level":1,"action":"real_commit","duration_ms":73.11,"total_stack_cleared":true} 
[2025-08-21 20:28:37] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4077,"business_type":"text_generation","business_id":null,"transaction_id":320,"freeze_id":320} 
[2025-08-21 20:28:37] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"estimated_cost":1.4077,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-21 20:28:37] production.INFO: AI生成任务记录创建成功 {"task_record_id":57,"external_task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"status":"pending","created_outside_transaction":true} 
