<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建WebSocket会话表（最终优化版）
 * 整合了所有后续修复的内容，直接创建最终结构
 *
 * 🔌 功能说明：
 * ✅ 包含优化后的WebSocket会话管理字段
 * ✅ 包含扩展字段：business_type、client_version等
 * ✅ 包含完整的索引和外键约束
 * ✅ 移除了冗余字段，添加了实际需要的字段
 *
 * 📝 版本历史：
 * - v1.0: 原始版本（包含connection_id等冗余字段）
 * - v2.0: 最终整合版本（本版本）
 *   * 删除：connection_id, ip_address, last_activity_at, connection_params, metadata
 *   * 添加：client_version, connection_ip, connection_info, subscribed_events,
 *           last_ping_at, message_count, disconnect_reason
 *   * 优化：status枚举值调整为 connected/disconnected/timeout
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('websocket_sessions', function (Blueprint $table) {
            // 基础字段
            $table->id()->comment('会话ID');
            $table->string('session_id', 100)->unique()->comment('会话标识符');
            $table->bigInteger('user_id')->unsigned()->nullable()->comment('用户ID，关联p_users表');

            // 客户端信息
            $table->string('client_type', 50)->default('python')->comment('客户端类型');
            $table->string('client_version', 20)->nullable()->comment('客户端版本');
            $table->string('business_type', 50)->nullable()->comment('业务类型：text_generation, storyboard_generation, project_creation等');

            // 连接状态
            $table->enum('status', ['connected', 'disconnected', 'timeout'])->default('connected')->comment('连接状态');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->string('connection_ip', 45)->nullable()->comment('客户端IP地址');

            // 连接信息
            $table->json('connection_info')->nullable()->comment('连接信息（JSON格式）');
            $table->json('subscribed_events')->nullable()->comment('订阅的事件列表（JSON格式）');

            // 时间字段
            $table->timestamp('connected_at')->useCurrent()->comment('连接时间');
            $table->timestamp('last_ping_at')->nullable()->comment('最后心跳时间');
            $table->timestamp('disconnected_at')->nullable()->comment('断开连接时间');

            // 统计字段
            $table->integer('message_count')->default(0)->comment('消息计数');
            $table->text('disconnect_reason')->nullable()->comment('断开连接原因');

            $table->timestamps();
            
            // 索引
            $table->index('session_id', 'idx_session_id');
            $table->index('user_id', 'idx_websocket_user');
            $table->index('client_type', 'idx_client_type');
            $table->index(['user_id', 'business_type'], 'idx_user_business_type');
            $table->index('status', 'idx_websocket_status');
            $table->index('connected_at', 'idx_connected_at');
            $table->index('last_ping_at', 'idx_last_ping');
            $table->index(['status', 'last_ping_at'], 'idx_status_ping');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->comment('WebSocket会话表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('websocket_sessions');
    }
};
