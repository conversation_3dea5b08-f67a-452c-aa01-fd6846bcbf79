<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI服务配置
    |--------------------------------------------------------------------------
    |
    | 🚨 架构边界规范：环境切换机制
    | ✅ 本地开发：工具API → AI模拟服务 → 模拟响应（无真实调用）
    | ✅ 生产环境：工具API → 真实AI平台 → 真实响应（真实调用）
    |
    | 环境切换通过 AI_SERVICE_MODE 环境变量控制：
    | - mock: 调用模拟服务（开发环境）
    | - real: 调用真实AI平台（生产环境）
    |
    */

    // 🚨 环境切换配置
    'service_mode' => env('AI_SERVICE_MODE', 'mock'), // mock | real

    // 模拟服务配置（开发环境）
    'mock_service' => [
        'base_url' => env('AI_MOCK_URL', 'https://aiapi.tiptop.cn'),
        'timeout' => env('AI_MOCK_TIMEOUT', 30),
        'enabled' => true,
    ],

    // 真实服务配置（生产环境）
    'real_service' => [
        'timeout' => env('AI_REAL_TIMEOUT', 60),
        'enabled' => true,
    ],
    
    // 🚨 AI平台配置（支持环境切换）
    'platforms' => [
        'deepseek' => [
            'name' => 'DeepSeek',
            'description' => '剧情生成和分镜脚本专家',
            'supports' => ['text_generation', 'text_generation_story', 'character_generation'],
            'mock_endpoint' => '/deepseek/chat/completions',
            'real_api' => [
                'base_url' => env('DEEPSEEK_API_URL', 'https://api.deepseek.com'),
                'api_key' => env('DEEPSEEK_API_KEY', ''),
                'endpoint' => '/chat/completions',
            ],
            'model' => 'deepseek-chat',
            'timeout' => 30,
        ],
        'liblib' => [
            'name' => 'LiblibAI',
            'description' => '图像生成专业平台',
            'supports' => ['image_generation'],
            'mock_endpoint' => '/api/open/xingliu/text2img',
            'real_api' => [
                'base_url' => env('LIBLIB_API_URL', 'https://openapi.liblibai.cloud'),
                'api_key' => env('LIBLIB_API_KEY', ''),
                'endpoint' => '/api/open/xingliu/text2img',
            ],
            'model' => 'star-3-alpha',
            'timeout' => 60,
        ],
        'kling' => [
            'name' => 'KlingAI',
            'description' => '视频生成领导者',
            'supports' => ['image_generation', 'video_generation'],
            'mock_endpoint' => '/kling/v1/images/generations',
            'real_api' => [
                'base_url' => env('KLING_API_URL', 'https://api.klingai.com'),
                'api_key' => env('KLING_API_KEY', ''),
                'endpoint' => '/v1/images/generations',
            ],
            'model' => 'kling-v2-master',
            'timeout' => 180,
        ],
        'minimax' => [
            'name' => 'MiniMax',
            'description' => '多模态AI平台',
            'supports' => ['text_generation', 'image_generation', 'voice_synthesis', 'video_generation', 'music_generation'],
            'mock_endpoint' => '/minimax/v1/text/chatcompletion_v2',
            'real_api' => [
                'base_url' => env('MINIMAX_API_URL', 'https://api.minimaxi.chat'),
                'api_key' => env('MINIMAX_API_KEY', ''),
                'group_id' => env('MINIMAX_GROUP_ID', ''),
                'endpoint' => '/v1/text/chatcompletion_v2',
            ],
            'model' => 'speech-01',
            'timeout' => 60,
        ],
        'volcengine' => [
            'name' => '火山引擎豆包',
            'description' => '专业语音AI平台',
            'supports' => ['voice_synthesis', 'sound_generation', 'music_generation'],
            'mock_endpoint' => '/volcengine/bigmodel/voices/synthesize',
            'real_api' => [
                'base_url' => env('VOLCENGINE_API_URL', 'https://openspeech.bytedance.com'),
                'api_key' => env('VOLCENGINE_API_KEY', ''),
                'app_id' => env('VOLCENGINE_APP_ID', ''),
                'endpoint' => '/api/v1/tts',
            ],
            'model' => 'doubao-voice',
            'timeout' => 60,
        ],
    ],

    // 默认平台配置
    'default_platforms' => [
        'text_generation' => 'deepseek',
        'text_generation_aistory' => 'deepseek',
        'text_generation_mystory' => 'deepseek',
        'text_generation_prompt' => 'deepseek',
        'text_generation_story' => 'deepseek',
        'image_generation' => 'liblib',
        'image_generation_character' => 'liblib',
        'image_generation_style' => 'liblib',
        'video_generation' => 'kling',
        'voice_synthesis' => 'minimax',
        'sound_generation' => 'volcengine',
        'music_generation' => 'minimax'
    ],

    // 生成参数默认值
    'generation_defaults' => [
        'text' => [
            'temperature' => 0.8,
            'max_tokens' => 1000,
            'top_p' => 0.9,
        ],
        'image' => [
            'aspect_ratio' => '16:9',
            'quality' => 'standard',
            'style' => 'realistic',
        ],
        'story' => [
            'length' => 'medium',
            'temperature' => 0.8,
            'max_tokens' => 2000,
        ],
        'character' => [
            'temperature' => 0.8,
            'max_tokens' => 1000,
        ],
    ],

    // 成本配置
    'cost_settings' => [
        'base_cost_per_token' => 0.0001,
        'platform_multipliers' => [
            'deepseek' => 1.0,
            'liblib' => 2.0,
            'kling' => 2.5,
            'minimax' => 1.5,
            'volcengine' => 1.8,
        ],
        'quality_multipliers' => [
            'standard' => 1.0,
            'hd' => 2.0,
            'ultra' => 3.0,
        ],
    ],

    // 重试配置
    'retry' => [
        'max_attempts' => 3,
        'delay_seconds' => 2,
        'backoff_multiplier' => 2,
    ],

    // 缓存配置
    'cache' => [
        'enabled' => env('AI_CACHE_ENABLED', true),
        'ttl' => env('AI_CACHE_TTL', 3600), // 1小时
        'prefix' => 'ai_generation:',
    ],

    // AI任务业务错误描述
    'business_error_keywords' => [
        '分镜失败',
        '缺少故事标题',
        '缺少',
        '格式不正确',
        '内容格式不对',
        '解析失败',
        '提示词生成失败',
        '故事生成失败',
        '角色生成失败',
        '风格生成失败',
        '图片生成失败',
        '视频生成失败',
        '音频生成失败',
        '音乐生成失败',
        '语音合成失败',
        '音效生成失败',
        '积分不足',
        '用户权限不足',
        '任务已存在',
        '参数验证失败'
    ],

    
    // 🚨 统一业务类型映射配置（替代各服务中的硬编码映射） 
    'business_type_mappings' => [
        // 前端业务类型 -> 后端任务类型映射
        'frontend_to_backend' => [
            'image' => 'image_generation',
            'video' => 'video_generation',
            'text' => 'text_generation',                    // 文生文返回无格式要求文本
            'prompt' => 'text_generation_prompt',           // 单独对生图提示词进行扩写
            'promptcharacter' => 'text_prompt_character',   // 单独对角色图片提示词进行扩写
            'promptstyle' => 'text_prompt_style',           // 单独对风格图片提示词进行扩写
            'story' => 'text_generation_story',             // 单独对故事进行扩写
            'aistory' => 'text_generation_aistory',         // 根据智能故事进行分镜：一次满足多条件
            'mystory' => 'text_generation_mystory',         // 根据自有故事进行分镜：一次满足多条件
            'character' => 'image_generation_character',    // 生成角色
            'style' => 'image_generation_style',            // 生成风格
            'sound' => 'sound_generation',
            'voice' => 'voice_synthesis',
            'music' => 'music_generation'
        ],

        // 业务类型 -> 支持平台映射（AiPlatformSelectionService）
        'business_to_platforms' => [
            'image' => ['liblib', 'kling', 'minimax'],
            'character' => ['liblib', 'kling', 'minimax'],
            'style' => ['liblib', 'kling', 'minimax'],
            'video' => ['kling', 'minimax'],
            'text' => ['deepseek', 'minimax'],
            'prompt' => ['deepseek', 'minimax'],
            'promptcharacter' => ['deepseek', 'minimax'],
            'promptstyle' => ['deepseek', 'minimax'],
            'story' => ['deepseek', 'minimax'],
            'storyboard' => ['deepseek', 'minimax'],
            'voice' => ['volcengine', 'minimax'],
            'sound' => ['volcengine', 'minimax'],
            'music' => ['minimax']
        ],

        // 任务类型 -> 支持平台映射（AiServiceClient）
        'task_to_platforms' => [
            'image_generation' => ['liblib', 'kling', 'minimax'],
            'image_generation_character' => ['liblib', 'kling', 'minimax'],
            'image_generation_style' => ['liblib', 'kling', 'minimax'],
            'video_generation' => ['kling', 'minimax'],
            'text_generation' => ['deepseek', 'minimax'],
            'text_generation_prompt' => ['deepseek', 'minimax'],
            'text_prompt_character' => ['deepseek', 'minimax'],
            'text_prompt_style' => ['deepseek', 'minimax'],
            'text_generation_story' => ['deepseek', 'minimax', 'volcengine'],
            'text_generation_aistory' => ['deepseek', 'minimax', 'volcengine'],
            'text_generation_mystory' => ['deepseek', 'minimax', 'volcengine'],
            'character_generation' => ['liblib', 'kling', 'minimax', 'deepseek'],
            'voice_synthesis' => ['minimax', 'volcengine'],
            'sound_generation' => ['volcengine', 'minimax'],
            'music_generation' => ['minimax']
        ]
    ],

    // 🚨 统一事件类型映射配置（替代各服务中的硬编码映射） 
    'event_type_mappings' => [
        'text_generation' => [
            'failed' => 'text_generation_failed',              // - 文本生成失败
            'completed' => 'text_generation_completed'         // - 文本生成完成
        ],
        'project_creation' => [
            'failed' => 'project_creation_failed',             // - 项目创建失败
            'completed' => 'project_creation_completed'        // - 项目创建完成
        ],
        'character_creation' => [
            'failed' => 'character_creation_failed',           // - 角色创建失败
            'completed' => 'character_creation_completed'      // - 角色创建完成
        ],
        'text_generation_aistory' => [
            'failed' => 'aistory_generation_failed',           // - 智能故事分镜生成失败
            'completed' => 'aistory_generation_completed'      // - 智能故事分镜生成完成
        ],
        'text_generation_mystory' => [
            'failed' => 'mystory_generation_failed',           // - 自有故事分镜生成失败
            'completed' => 'mystory_generation_completed'      // - 自有故事分镜生成完成
        ],
        'text_generation_prompt' => [
            'failed' => 'prompt_generation_failed',            // - 分镜图片提示词扩写失败
            'completed' => 'prompt_generation_completed'       // - 分镜图片提示词扩写成功
        ],
        'text_prompt_character' => [
            'failed' => 'prompt_character_failed',             // - 角色图片提示词扩写失败
            'completed' => 'prompt_character_completed'        // - 角色图片提示词扩写成功
        ],
        'text_prompt_style' => [
            'failed' => 'prompt_style_failed',                 // - 风格图片提示词扩写失败
            'completed' => 'prompt_style_completed'            // - 风格图片提示词扩写成功
        ],
        'text_generation_story' => [
            'failed' => 'story_generation_failed',             // - 故事扩写失败
            'completed' => 'story_generation_completed'        // - 故事扩写成功
        ],
        'image_generation_character' => [
            'failed' => 'image_character_generation_failed',   // - 图像角色生成失败
            'completed' => 'image_character_generation_completed' // - 图像角色生成成功
        ],
        'image_generation_style' => [
            'failed' => 'style_generation_failed',             // - 生成风格失败
            'completed' => 'style_generation_completed'        // - 生成风格成功
        ],
        'image_generation' => [
            'failed' => 'image_generation_failed',             // - 分镜图片生成失败
            'completed' => 'image_generation_completed'        // - 分镜图片生成完成
        ],
        'video_generation' => [
            'failed' => 'video_generation_failed',             // - 视频生成失败
            'completed' => 'video_generation_completed'        // - 视频生成完成
        ],
        'voice_synthesis' => [
            'failed' => 'voice_synthesis_failed',              // - 语音合成失败
            'completed' => 'voice_synthesis_completed'         // - 语音合成完成
        ],
        'voice_audition' => [
            'failed' => 'voice_audition_failed',               // - 语音+音色试听失败
            'completed' => 'voice_audition_completed'          // - 语音+音色试听完成
        ],
        'sound_generation' => [
            'failed' => 'sound_generation_failed',             // - 音效生成失败
            'completed' => 'sound_generation_completed'        // - 音效生成完成
        ],
        'music_generation' => [
            'failed' => 'music_generation_failed',             // - 生成音乐失败
            'completed' => 'music_generation_completed'        // - 生成音乐完成
        ],
        'audio_mix' => [
            'failed' => 'audio_mix_failed',                    // - 音频混合失败
            'completed' => 'audio_mix_completed'               // - 音频混合完成
        ],
        'audio_enhance' => [
            'failed' => 'audio_enhance_failed',                // - 音频增强失败
            'completed' => 'audio_enhance_completed'           // - 音频增强完成
        ]
    ],

    // 🚨 平台时间预估配置（AiServiceClient）
    'platform_time_estimates' => [
        'deepseek' => [
            'text_generation' => '5-15秒',
            'text_generation_story' => '10-30秒',
            'text_generation_prompt' => '8-20秒',
            'text_generation_aistory' => '15-45秒',
            'text_generation_mystory' => '15-45秒',
            'character_generation' => '8-20秒'
        ],
        'liblib' => [
            'image_generation' => '30-90秒',
            'image_generation_character' => '35-95秒',
            'image_generation_style' => '25-85秒'
        ],
        'kling' => [
            'image_generation' => '20-60秒',
            'image_generation_character' => '25-65秒',
            'image_generation_style' => '20-60秒',
            'video_generation' => '2-5分钟'
        ],
        'minimax' => [
            'image_generation' => '25-75秒',
            'image_generation_character' => '30-80秒',
            'image_generation_style' => '25-75秒',
            'video_generation' => '3-8分钟',
            'text_generation' => '8-25秒',
            'text_generation_story' => '12-35秒',
            'text_generation_prompt' => '10-30秒',
            'text_generation_aistory' => '15-45秒',
            'text_generation_mystory' => '15-45秒',
            'voice_synthesis' => '15-45秒',
            'sound_generation' => '20-60秒',
            'music_generation' => '1-3分钟'
        ],
        'volcengine' => [
            'voice_synthesis' => '10-30秒',
            'sound_generation' => '15-45秒',
            'text_generation' => '6-18秒',
            'text_generation_story' => '10-25秒',
            'text_generation_prompt' => '8-20秒',
            'text_generation_aistory' => '15-45秒',
            'text_generation_mystory' => '15-45秒',
        ]
    ],

    'prompt_temp' => [
        'image_generation' => '',
        'video_generation' => '',
        'text_generation' => '',
        // 分镜提示词扩写
        'text_generation_prompt' =>
'任务要求：基于分镜剧情和原始提示词，生成优化后的分镜图片提示词。新提示词需准确描述剧情中的角色姿势动作(Character Pose & Action)、场景环境、关键事件和情感氛围，并包含拍摄角度(Camera Angle)、光线(Lighting)、构图(Composition)等关键视觉元素，确保AI能精确复现分镜内容。每项内容限制在100个汉字以内，严格按以下JSON格式返回。

注意事项：
- 提示词必须具体详细，突出关键视觉元素
- 禁止使用"无"、"未知"、"不确定"等模糊信息
- 重点描述角色姿势动作(Character Pose & Action)、场景环境、拍摄角度(Camera Angle)、光线(Lighting)、构图(Composition)等关键视觉元素
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
    "分镜图片生成提示词": "【优化后的分镜图片描述提示词】"
}',
        // 角色提示词扩写
        'text_prompt_character' =>  
'任务要求：基于角色描述和设定，生成详细的角色图片提示词。提示词需包含角色的外貌特征、服装风格、表情神态、姿态动作(Pose and Actions)等关键视觉元素，确保AI能准确生成符合角色设定的图片。每项内容限制在100个汉字以内，严格按以下JSON格式返回。

注意事项：
- 提示词必须具体明确，避免模糊描述
- 禁止使用"无"、"未知"、"不确定"等不明确信息
- 重点突出角色的独特视觉特征
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
    "角色名称": "【角色名称】",
    "提示词": "【详细的角色视觉描述提示词】",
    "类型": "【人类 或 拟人动物 或 原生动物】",
    "性别": "【男 或 女】",
    "年龄阶段": "【婴幼儿(0-6岁) 或 儿童(6-12岁) 或 少年(12-18岁) 或 青年(18-40岁) 或 中年(40-65岁) 或 老年(65岁以上)】"
    "TAG": ["【标签1】", "【标签2】", "【标签3】", "【标签4】", "【标签5】"]
}',
        // 风格提示词扩写
        'text_prompt_style' =>
'任务要求：基于风格描述和应用场景，生成AI生图应用场景的风格参考图片提示词。提示词需包含艺术风格、色彩搭配(Color Palette)、画面构图(Composition)、视觉效果、氛围营造等关键风格元素，确保AI能准确生成符合应用场景的风格参考图片。每项内容限制在100个汉字以内，严格按以下JSON格式返回。

注意事项：
- 提示词必须具体明确，突出风格特色
- 禁止使用"无"、"未知"、"不确定"等不明确信息
- 重点描述视觉风格和艺术表现手法(Artistic Techniques)
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
    "风格名称": "【风格名称】",
    "风格描述": "【风格描述】",
    "提示词": "【详细的风格视觉描述提示词】",
    "TAG": ["【标签1】", "【标签2】", "【标签3】", "【标签4】", "【标签5】"]
}',
        // 故事扩写
        'text_generation_story' =>
'任务要求：基于原始故事大纲或简要情节，进行故事内容的扩写和丰富。扩写后的故事需包含详细的情节发展、角色对话(Character Dialogue)、场景描述、情感表达等关键叙事元素，确保故事内容更加生动完整。每项内容限制在500个汉字以内，严格按以下JSON格式返回。

注意事项：
- 故事内容必须连贯完整，情节发展合理
- 禁止使用"无"、"未知"、"不确定"等不明确信息
- 重点丰富角色刻画(Character Development)和情节细节
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
    "扩写后的故事内容": "【详细完整的故事内容】"
}',
        // 智能故事分镜
        'text_generation_aistory' => 
'任务要求：基于`故事大纲`，创作一个由三个场景组成的AI视频剧本，每个场景包含5-6个分镜(Shots)，支持1分钟播放时长。每项内容限制在100个汉字以内，严格按以下JSON格式返回。

注意事项：
- 所有内容必须具体明确，禁止使用"无"、"未知"、"不确定"等模糊信息
- 角色名称保持一致性
- 分镜提示词需包含关键视觉元素
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
  "故事标题": "【提取故事标题】",
  "故事简概": "【一句话故事摘要】",
  "场景【场景序号】": {
    "场景名称": "【具体物理场景名称】",
    "空间": "【室内或室外】",
    "时间": "【具体时间信息】",
    "天气": "【天气状况】",
    "场景提示词": "【根据空间、时间、天气及剧情设置与“场景名称”相呼应的AI生图的物理场景提示词】",
    "分镜": [
      {
        "分镜序号": 【分镜序号】,
        "出境角色": "【角色名称，多角色用逗号分隔】",
        "字幕": "【分镜旁白或字幕】",
        "分镜提示词": "【生成与“场景提示词”相呼应的分镜事件提示词，必须包含复现场景静态或动态事件的多个AI生图技术参数组合搭配提示词，如：角色姿势动作(Character Pose & Action)、场景环境(Scene Environment)、拍摄角度(Camera Angle)、光线(Lighting)、构图(Composition)等关键视觉元素(Key Visual Elements)】"
      }
    ]
  }
}',
        // 自有故事分镜
        'text_generation_mystory' => 
'任务要求：基于`自有故事`，在不改变故事任何文字的前提下将多句合并成一个分镜(Shots)，要求JSON模板中的每一项内容的长度不超过100个汉字，严格按以下格式返回。

注意事项：
- 所有内容必须具体明确，禁止使用"无"、"未知"、"不确定"等模糊信息
- 角色名称保持一致性
- 分镜提示词需包含关键视觉元素
- 【】内容为占位符，需要替换为实际内容

返回格式：
{
  "故事标题": "【提取故事标题】",
  "故事简概": "【一句话故事摘要】",
  "场景【场景序号】": {
    "场景名称": "【具体物理场景名称】",
    "空间": "【室内或室外】",
    "时间": "【具体时间信息】",
    "天气": "【天气状况】",
    "场景提示词": "【根据空间、时间、天气及剧情设置与“场景名称”相呼应的AI生图的物理场景提示词】",
    "分镜": [
      {
        "分镜序号": 【分镜序号】,
        "出境角色": "【角色名称，多角色用逗号分隔】",
        "字幕": "【分镜旁白或字幕】",
        "分镜提示词": "【生成与“场景提示词”相呼应的分镜事件提示词，必须包含复现场景静态或动态事件的多个AI生图技术参数组合搭配提示词，如：角色姿势动作(Character Pose & Action)、场景环境、拍摄角度(Camera Angle)、光线(Lighting)、构图(Composition)等关键视觉元素】"
      }
    ]
  }
}',
        'image_generation_character' => '',
        'image_generation_style' => '',
        'sound_generation' => '',
        'voice_synthesis' => '',
        'music_generation' => ''
    ],

    /**
     * 获取第三方服务配置
     */
    'third_party_config' => [
        'service_mode' => env('THIRD_PARTY_MODE', 'mock'), // mock | real
        'mock_service' => [
            'base_url' => env('THIRD_PARTY_MOCK_URL', 'https://thirdapi.tiptop.cn'),
            'timeout' => env('THIRD_PARTY_MOCK_TIMEOUT', 30),
        ],
        'platforms' => [
            'wechat' => [
                'mock_endpoint' => '/wechat/oauth/authorize',
                'real_api' => [
                    'app_id' => env('WECHAT_APP_ID', ''),
                    'app_secret' => env('WECHAT_APP_SECRET', ''),
                ],
            ],
            'alipay' => [
                'mock_endpoint' => '/alipay/trade/page/pay',
                'real_api' => [
                    'app_id' => env('ALIPAY_APP_ID', ''),
                    'private_key' => env('ALIPAY_PRIVATE_KEY', ''),
                ],
            ],
            'sms' => [
                'mock_endpoint' => '/sms/aliyun/send',
                'real_api' => [
                    'access_key' => env('ALIYUN_ACCESS_KEY', ''),
                    'access_secret' => env('ALIYUN_ACCESS_SECRET', ''),
                ],
            ],
            'email' => [
                'mock_endpoint' => '/email/smtp/send',
                'real_api' => [
                    'smtp_host' => env('MAIL_HOST', ''),
                    'smtp_username' => env('MAIL_USERNAME', ''),
                    'smtp_password' => env('MAIL_PASSWORD', ''),
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 🚨 环境切换辅助函数
    |--------------------------------------------------------------------------
    */

    /**
     * 获取当前服务模式
     */
    'get_service_mode' => function() {
        return config('ai.service_mode', 'mock');
    },

    /**
     * 判断是否为模拟模式
     */
    'is_mock_mode' => function() {
        return config('ai.service_mode', 'mock') === 'mock';
    },

    /**
     * 获取平台API配置
     */
    'get_platform_config' => function($platform) {
        $config = config("ai.platforms.{$platform}");
        if (!$config) {
            return null;
        }

        $serviceMode = config('ai.service_mode', 'mock');

        if ($serviceMode === 'mock') {
            // 模拟模式：使用模拟服务
            return [
                'base_url' => config('ai.mock_service.base_url'),
                'endpoint' => $config['mock_endpoint'],
                'timeout' => config('ai.mock_service.timeout'),
                'mode' => 'mock'
            ];
        } else {
            // 真实模式：使用真实API
            return [
                'base_url' => $config['real_api']['base_url'],
                'endpoint' => $config['real_api']['endpoint'],
                'api_key' => $config['real_api']['api_key'],
                'timeout' => config('ai.real_service.timeout'),
                'mode' => 'real'
            ];
        }
    },
];
