<?php

namespace App\Console\Commands;

use Throwable;
use Swoole\Table;
use Swoole\Timer;
use Swoole\Coroutine;
use App\Enums\ApiCodeEnum;
use App\Exceptions\ApiException;
use Swoole\WebSocket\Server;
use Illuminate\Console\Command;
use App\WebSocket\HandleMessage;
use App\Services\PyApi\WebSocketService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WebSocketServer extends Command
{
    /**
     * 命令名称
     * 支持两种模式：传统模式和现代会话模式
     * 执行命令 : php artisan websocket:serve
     * @var string
     */
    protected $signature = 'websocket:serve {--port=} {--host=} {--mode=}';

    /**
     * 命令描述
     * @var string
     */
    protected $description = '启动WebSocket服务 (支持传统模式和现代会话模式)';

    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        parent::__construct();
        $this->webSocketService = $webSocketService;
    }

    public function handle()
    {
        $mode = $this->option('mode') ?: config('websocket.server.mode');
        $host = $this->option('host') ?: config('websocket.server.host');
        $port = $this->option('port') ?: config('websocket.server.port');

        $this->info("Starting WebSocket server on {$host}:{$port} in {$mode} mode");

        // 检查Swoole扩展
        if (!extension_loaded('swoole')) {
            $this->error('Swoole extension is not installed. Please install swoole extension first.');
            return 1;
        }

        if ($mode === 'modern') {
            return $this->handleModernMode($host, $port);
        } else {
            return $this->handleLegacyMode($host, $port);
        }
    }

    /**
     * 现代会话模式 (来自WebSocketServerCommand)
     */
    private function handleModernMode($host, $port)
    {
        try {
            // 记录服务器启动时间
            Cache::put('websocket_server_start_time', Carbon::now(), 86400);

            // 创建WebSocket服务器：以下报错是IDE无法识别Swoole类型
            /** @var mixed $server WebSocket服务器实例 */
            $server = new \Swoole\WebSocket\Server($host, $port);

            // 配置服务器
            $server->set([
                'worker_num' => config('websocket.performance.worker_num'),
                'heartbeat_check_interval' => config('websocket.performance.heartbeat_check_interval'),
                'heartbeat_idle_time' => config('websocket.performance.heartbeat_idle_time'),
                'max_connection' => config('websocket.performance.max_connection'),
                'package_max_length' => config('websocket.performance.package_max_length'),
                'enable_coroutine' => true,
                'log_file' => storage_path('logs/' . config('websocket.logging.file')),
                'log_level' => 4, // SWOOLE_LOG_INFO
            ]);

            // 监听连接打开事件
            $server->on('open', function ($server, $request) {
                $this->handleModernOpen($server, $request);
            });

            // 监听消息事件
            $server->on('message', function ($server, $frame) {
                $this->handleModernMessage($server, $frame);
            });

            // 监听连接关闭事件
            $server->on('close', function ($server, $fd) {
                $this->handleModernClose($server, $fd);
            });

            // 监听Worker启动事件，启动Redis订阅协程
            $server->on('workerstart', function ($server, $workerId) {
                if ($workerId === 0) { // 只在第一个Worker中启动Redis订阅
                    $this->startRedisSubscription($server);

                    // 🔧 修复：启动按session_id的心跳检查定时器 (每2分钟执行一次)
                    // 现代模式：按session_id精确检查心跳，支持同用户多连接
                    \Swoole\Timer::tick(120000, function () {
                        try {
                            // 获取所有活跃的WebSocket会话
                            $activeSessions = \App\Models\WebSocketSession::where('status', 'connected')
                                ->where('last_ping_at', '>', \Carbon\Carbon::now()->subMinutes(10)) // 10分钟内有心跳的会话
                                ->get();

                            $totalSessions = $activeSessions->count();
                            $timeoutSessions = 0;

                            // 🚀 修复：按session_id逐个检查心跳，避免误断同用户的其他连接
                            foreach ($activeSessions as $session) {
                                // 如果超过5分钟没有心跳，标记为超时
                                if ($session->last_ping_at && $session->last_ping_at->diffInMinutes(\Carbon\Carbon::now()) > 5) {
                                    // 只断开当前session_id的连接，不影响同用户的其他连接
                                    $session->markAsTimeout();
                                    $timeoutSessions++;

                                    echo "⏰ 现代模式会话超时: session_id={$session->session_id}, user_id={$session->user_id}, business_type={$session->business_type}\n";
                                }
                            }

                            echo "💓 现代模式批量心跳检查完成: 活跃会话数={$totalSessions}, 超时会话数={$timeoutSessions}\n";

                        } catch (\Exception $e) {
                            echo "❌ 现代模式批量心跳检查失败: " . $e->getMessage() . "\n";

                            \Illuminate\Support\Facades\Log::error('现代模式WebSocket批量心跳检查异常', [
                                'error' => $e->getMessage(),
                                'trace' => $e->getTraceAsString(),
                                'timestamp' => \Carbon\Carbon::now()->format('c')
                            ]);
                        }
                    });
                }
            });

            $this->info("WebSocket server started successfully in modern mode!");
            $this->info("Listening on wss://{$host}:{$port}");

            // 启动服务器
            $server->start();

        } catch (\Exception $e) {
            $this->error("Failed to start WebSocket server: " . $e->getMessage());
            Log::error('WebSocket服务器启动失败', ['error' => $e->getMessage()]);
            return 1;
        }
    }

    /**
     * 传统模式 (原有的SSL模式)
     */
    private function handleLegacyMode($host, $port)
    {
        // ======================= SSL配置 - 开始 =======================
        // 从配置文件获取SSL证书路径
        $ssl_cert_file = config('websocket.ssl.cert_file');
        $ssl_key_file = config('websocket.ssl.key_file');

        // 如果配置文件未配置，使用默认路径
        if (!$ssl_cert_file || !$ssl_key_file) {
            $basePath = __DIR__ . '/ssl';
            $ssl_cert_file = $basePath.'/api.tiptop.cn.pem';
            $ssl_key_file = $basePath.'/api.tiptop.cn.key';
        } else {
            // 检查是否为绝对路径，如果不是则转换为绝对路径
            if (!$this->isAbsolutePath($ssl_cert_file)) {
                $ssl_cert_file = base_path($ssl_cert_file);
            }
            if (!$this->isAbsolutePath($ssl_key_file)) {
                $ssl_key_file = base_path($ssl_key_file);
            }
        }

        // 检查证书文件是否存在，如果不存在则提示错误并退出
        if (!file_exists($ssl_cert_file) || !file_exists($ssl_key_file)) {
            $this->error("SSL certificate files not found!");
            $this->error("Cert file path: " . $ssl_cert_file);
            $this->error("Key file path:  " . $ssl_key_file);
            $this->info("Please configure SSL certificate paths in config/websocket.php file:");
            $this->info("'ssl' => [");
            $this->info("    'cert_file' => 'path/to/certificate.pem',");
            $this->info("    'key_file' => 'path/to/private.key',");
            $this->info("],");
            $this->info("Or use 'mkcert api.tiptop.cn' to generate them in the default location");
            return 1;
        }

        // 创建服务器时，第四个参数需要增加 SWOOLE_SSL 来启用加密：以下报错是IDE无法识别Swoole类型
        /** @var mixed $server SSL WebSocket服务器实例 */
        $server = new Server(
            $host,
            $port,
            3, // SWOOLE_PROCESS
            1 | 512 // SWOOLE_SOCK_TCP | SWOOLE_SSL
        );
        
        $server->set([
            // 添加SSL证书和密钥文件配置
            'ssl_cert_file' => $ssl_cert_file,
            'ssl_key_file'  => $ssl_key_file,

            // 你原来的配置
            'reload_async' => true,
            'max_wait_time' => 60,
            'open_eof_check' => true,
            'package_eof' => '}',
            'heartbeat_check_interval' => 25,// 心跳间隔，单位秒
            'heartbeat_idle_time' => 60,// 空闲时间，单位秒
        ]);
        // ======================= SSL配置 - 结束 =======================

        // 以下报错是IDE无法识别Swoole类型
        /** @var mixed $table Swoole内存表实例 */
        $table = new Table(1024 * 100);
        $table->column('user_id', Table::TYPE_INT);
        $table->column('session_id', Table::TYPE_STRING, 64);
        $table->column('ping_at', Table::TYPE_INT);            // 最后收到心跳的时间
        $table->create();

        $handle = new HandleMessage();

        $this->info("WebSocket Server starting with SSL enabled in legacy mode...");
        $this->info("Listening on wss://{$host}:{$port}");

        $server->on('workerstart', function ($server, $worker_id) use($table, $handle)
        {
            if ($worker_id == 0) // 仅在worker0进程启动定时器和Redis订阅
            {
                // 启动Redis订阅协程
                $this->startRedisSubscription($server);

                // 🔧 优化：移除25秒定时器，依赖客户端主动心跳和Swoole内置超时机制
                // 客户端每30秒发送心跳，Swoole的heartbeat_idle_time=60秒会自动断开无响应连接
                $this->info("💓 心跳机制优化: 依赖客户端30秒主动心跳 + Swoole 60秒超时断开");

                // 🔧 修复：确保定时器只在Worker 0中执行，避免重复执行
                if ($server->worker_id === 0) {
                    Timer::tick(120000, function () use ($table, $server) {
                    try {
                        $sessionHeartbeats = [];  // 🔧 修复：改为按session_id记录心跳
                        $totalConnections = 0;

                        // 从缓存获取所有活跃连接的心跳数据
                        foreach ($server->connections as $fd) {
                            if ($server->exist($fd) && $server->isEstablished($fd)) {
                                $cacheKey = "websocket_heartbeat_{$fd}";
                                $cacheData = \Illuminate\Support\Facades\Cache::get($cacheKey);

                                if ($cacheData && isset($cacheData['session_id']) && isset($cacheData['ping_at'])) {
                                    $totalConnections++;
                                    $sessionId = (string) $cacheData['session_id'];
                                    $pingAt = (int) $cacheData['ping_at'];

                                    // 🚀 修复：按session_id记录心跳，支持同用户多连接
                                    $sessionHeartbeats[$sessionId] = $pingAt;
                                }
                            }
                        }

                        if (empty($sessionHeartbeats)) {
                            echo "💓 批量心跳更新: 无活跃会话连接 (总连接数: {$totalConnections})\n";
                            return;
                        }

                        // 🔧 修复：调用新的按session_id批量更新方法
                        $updatedCount = \App\Models\WebSocketSession::batchUpdateHeartbeatBySessionId($sessionHeartbeats);

                        echo "💓 批量心跳更新完成: 总连接数={$totalConnections}, 会话数=" . count($sessionHeartbeats) . ", 更新会话数={$updatedCount}\n";

                    } catch (\Exception $e) {
                        echo "❌ 批量心跳更新失败: " . $e->getMessage() . "\n";

                        \Illuminate\Support\Facades\Log::error('WebSocket批量心跳更新异常', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString(),
                            'timestamp' => \Carbon\Carbon::now()->format('c')
                        ]);
                    }
                });
                }
            }
        });

        $server->on('open', function ($server, $request) use ($table, $handle)
        {
            $this->info("🔵 Legacy模式连接建立: #" . $request->fd);

            // 🔒 基于Session的安全验证策略
            $sessionId = $request->get['session_id'] ?? null;

            if (!$sessionId) {
                $this->sendErrorAndClose($server, $request->fd, 400, 'session_id参数缺失');
                return;
            }

            // 🚀 新增：检查是否已有心跳活动，如果有则跳过认证缓存验证
            $userId = null;
            $skipAuthCache = false;

            // 先检查数据库中是否已有该session的记录且有心跳活动
            $existingSession = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if ($existingSession && $existingSession->last_ping_at) {
                $minutesSinceLastPing = $existingSession->last_ping_at->diffInMinutes(Carbon::now());
                if ($minutesSinceLastPing <= 5) {
                    // 有最近心跳活动，跳过认证缓存验证
                    $userId = $existingSession->user_id;
                    $skipAuthCache = true;

                    $this->info("🔍 Legacy模式检测到心跳活动，跳过认证缓存验证: session_id={$sessionId}, user_id={$userId}, 最后心跳={$minutesSinceLastPing}分钟前");
                }
            }

            if (!$skipAuthCache) {
                // 验证Session是否有效并获取用户信息（缓存 websocket_auth_{$sessionId} 在 php\api\app\Services\PyApi\WebSocketService.php 中创建）
                $sessionKey = "websocket_auth_{$sessionId}";
                $sessionData = Cache::get($sessionKey);

                // 🔍 添加详细的调试信息
                // $this->info("🔍 WebSocket Session验证调试信息:");
                // $this->info("   - Session ID: {$sessionId}");
                // $this->info("   - Cache Key: {$sessionKey}");
                // $this->info("   - Session Data: " . ($sessionData ? json_encode($sessionData) : 'null'));

                if (!$sessionData) {
                    $this->error("❌ Session验证失败: Session不存在或已过期");
                    $this->sendErrorAndClose($server, $request->fd, 401, 'Session不存在或已过期');
                    return;
                }

                if (!isset($sessionData['user_id']) || empty($sessionData['user_id'])) {
                    $this->sendErrorAndClose($server, $request->fd, 401, 'Session中缺少用户信息');
                    return;
                }

                // 验证Session是否过期
                if (isset($sessionData['expires_at'])) {
                    $expiresAt = Carbon::parse($sessionData['expires_at']);
                    if ($expiresAt->isPast()) {
                        $this->sendErrorAndClose($server, $request->fd, 401, 'Session已过期');
                        return;
                    }
                }

                // 获取已验证的用户ID
                $userId = $sessionData['user_id'];
            }

            // 用户身份已通过Session验证获取
            $this->info("🔍 Legacy模式连接验证: session_id={$sessionId}, user_id={$userId}");

            try {
                // 🚀 使用统一的session验证逻辑
                $validationResult = $this->validateSessionForConnection($sessionId, $userId, $server, $request->fd);

                if (!$validationResult['success']) {
                    // 验证失败，已经在validateSessionForConnection中处理了响应
                    return;
                }

                $session = $validationResult['session'];

                // 更新会话状态为已连接（注意：不在此处更新last_ping_at，让心跳检查逻辑有意义）
                $session->status = 'connected';
                $session->connected_at = Carbon::now();
                // 🔧 修复：不在连接时立即更新last_ping_at，保持原有的心跳时间用于检查逻辑
                $session->save();

                // 🔧 优化：在内存表中存储会话信息，简化心跳数据
                $tableData = [
                    'user_id' => $session->user_id,
                    'session_id' => $sessionId,
                    'ping_at' => time()            // 最后收到心跳的时间
                ];
                $table->set($request->fd, $tableData);

                // 在缓存中存储双向映射
                Cache::put("websocket_fd_{$request->fd}", $sessionId, 3600);
                Cache::put("websocket_session_{$sessionId}", $request->fd, 3600);

                $this->info("✅ Legacy模式会话连接成功: {$sessionId}, 用户ID: {$session->user_id}, FD: {$request->fd}");

            } catch (\Exception $e) {
                $this->info("❌ 会话处理失败: " . $e->getMessage());
                $server->close($request->fd);
            }
        });

        $server->on('message', function ($server, $frame) use ($table, $handle) {
            $this->info("🔵 Legacy模式收到消息 from #" . $frame->fd . ": " . $frame->data);

            // 检查是否是现代模式的心跳消息格式
            $modernPingCheck = json_decode($frame->data, true);
            if ($modernPingCheck && isset($modernPingCheck['type']) && $modernPingCheck['type'] === 'ping')
            {
                $this->info("💓 检测到现代模式心跳消息，处理中...");

                // 🔧 修复：收到现代模式ping时，更新心跳时间并清除等待状态
                $sessionData = $table->get($frame->fd);

                if ($sessionData)
                {
                    // 更新心跳时间
                    $sessionData['ping_at'] = time();
                    $table->set($frame->fd, $sessionData);

                    // 同时将心跳数据存储到缓存
                    $cacheKey = "websocket_heartbeat_{$frame->fd}";
                    $cacheData = [
                        'user_id' => $sessionData['user_id'],
                        'session_id' => $sessionData['session_id'],
                        'ping_at' => $sessionData['ping_at'],
                        'fd' => $frame->fd,
                        'updated_at' => time()
                    ];
                    Cache::put($cacheKey, $cacheData, 600); // 10分钟过期
                } else {
                    echo "❌ 现代模式ping处理失败，内存表中没有FD={$frame->fd}的数据\n";
                    echo "🔍 尝试遍历内存表查看所有数据:\n";
                    foreach ($table as $fd => $data) {
                        echo "   FD={$fd}: " . json_encode($data) . "\n";
                    }
                }

                // 🔧 优化：不再立即更新数据库，只记录日志
                try {
                    $sessionData = $table->get($frame->fd);
                    if ($sessionData && isset($sessionData['session_id'])) {
                        $sessionId = (string) $sessionData['session_id'];
                        $fd = (int) $frame->fd;
                        $pingTime = Carbon::createFromTimestamp($sessionData['ping_at'])->format('c');

                        $this->info("💓 Legacy模式心跳处理: session_id={$sessionId}, fd={$fd}, ping_at={$pingTime}");

                        // 发送现代模式格式的pong响应
                        $pongResponse = json_encode([
                            'event' => 'pong',
                            'message' => "内存表心跳时间更新成功: session_id={$sessionId}, ping_at={$pingTime}, fd={$fd}",
                            'data' => [
                                'server_time' => Carbon::now()->format('c'),
                            ]
                        ]);
                    } else {
                        // 会话数据不存在
                        $pongResponse = json_encode([
                            'event' => 'pong',
                            'message' => "会话数据不存在",
                            'data' => [
                                'server_time' => Carbon::now()->format('c'),
                            ]
                        ]);
                    }
                } catch (\Exception $e) {
                    // 发送现代模式格式的pong响应
                    $pongResponse = json_encode([
                        'event' => 'pong',
                        'message' => "心跳处理异常：". $e->getMessage(),
                        'data' => [
                            'server_time' => Carbon::now()->format('c'),
                        ]
                    ]);

                    $this->error("❌ Legacy模式心跳处理失败: " . $e->getMessage());
                }

                // 推送到前端
                $server->push($frame->fd, $pongResponse);
                return;
            }

            $event = '';
            $user_id = 0;
            $requests = [];
            $response = ['code' => ApiCodeEnum::SYSTEM_ERROR, 'message' => '发生错误', 'data' => []];
            try {
                $user_id = $table->get($frame->fd, 'user_id');
                list($event, $requests) = $handle->getRequestData($frame->data, $frame->fd);

                // 🔧 修复：处理pong响应，更新心跳状态
                if($event == 'pong') {
                    $existingData = $table->get($frame->fd);
                    if ($existingData) {
                        $existingData['ping_at'] = time();        // 更新最后收到pong的时间
                        $existingData['ping_waiting'] = 0;        // 清除等待状态
                        $existingData['ping_sent_at'] = 0;        // 清除发送时间
                        $table->set($frame->fd, $existingData);

                        $this->info("✅ 收到pong响应 FD={$frame->fd}，心跳时间已更新");
                    }
                    return false;
                }

                if(empty($event))
                {
                    return false;
                }
                // 所有WebSocket事件都直接调用对应的控制器，由控制器内部验证token
                list($class, $action) = $handle->event2Controller($event);
                $response = (new $class)->$action($requests);
            }
            catch (Throwable $e)
            {
                \Illuminate\Support\Facades\Log::error($e->getMessage().PHP_EOL.'file:'.$e->getFile().PHP_EOL.'line:'.$e->getLine());
                if($e instanceof ApiException)
                {
                    $response = ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => $e->getData()];
                } else {
                    $url = $event;
                    if(!empty($user_id))
                    {
                        $url .= ' @ ' . $user_id;
                    }
                    // 直接记录错误日志而不是使用队列
                    \Illuminate\Support\Facades\Log::error('WebSocket error', [
                        'url' => $url,
                        'message' => $e->getMessage(),
                        'params' => $frame->data,
                        'user_id' => $user_id
                    ]);
                    $response = ['code' => 500, 'message' => '发生错误', 'data' => []];
                }
            }
            $response['event'] = $event;
            $response['uid'] = $requests['uid'] ?? 0;
            $this->info("Push to #" . $frame->fd . ": " . json_encode($response));
            $server->push($frame->fd, $handle->encodeMessage($response));
        });

        $server->on('close', function ($server, $fd) use ($table) {
            $this->info('🔴 Legacy模式连接关闭: #' . $fd);

            // 获取会话ID
            $sessionId = Cache::get("websocket_fd_{$fd}");

            if ($sessionId) {
                try {
                    // 更新数据库会话状态
                    $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                    if ($session) {
                        $session->status = 'disconnected';
                        $session->disconnect_reason = '客户端断开连接';
                        $session->disconnected_at = Carbon::now();
                        $session->save();

                        $this->info("✅ Legacy模式会话已断开: {$sessionId}");
                    }

                    // 清理双向映射缓存
                    Cache::forget("websocket_fd_{$fd}");
                    Cache::forget("websocket_session_{$sessionId}");

                } catch (\Exception $e) {
                    $this->info("❌ 会话清理失败: " . $e->getMessage());
                }
            }

            if($table->exist($fd))
            {
                $user_id = $table->get($fd, 'user_id');
                $table->delete($fd);

                if(!empty($user_id))
                {
                    // 记录用户离线日志
                    \Illuminate\Support\Facades\Log::info('User offline', ['user_id' => $user_id]);
                }
            }
        });

        $server->start();
    }

    /**
     * 现代模式：处理连接打开
     */
    private function handleModernOpen($server, $request)
    {
        $fd = $request->fd;
        $sessionId = $request->get['session_id'] ?? null;

        if (!$sessionId) {
            $this->sendErrorAndClose($server, $fd, 400, 'session_id参数缺失');
            return;
        }

        // 🚀 新增：检查是否已有心跳活动，如果有则跳过认证缓存验证
        $userId = null;
        $skipAuthCache = false;

        // 先检查数据库中是否已有该session的记录且有心跳活动
        $existingSession = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
        if ($existingSession && $existingSession->last_ping_at) {
            $minutesSinceLastPing = $existingSession->last_ping_at->diffInMinutes(Carbon::now());
            if ($minutesSinceLastPing <= 5) {
                // 有最近心跳活动，跳过认证缓存验证
                $userId = $existingSession->user_id;
                $skipAuthCache = true;

                $this->info("🔍 Modern模式检测到心跳活动，跳过认证缓存验证: session_id={$sessionId}, user_id={$userId}, 最后心跳={$minutesSinceLastPing}分钟前");
            }
        }

        if (!$skipAuthCache) {
            // 基于Session的安全验证策略（缓存 websocket_auth_{$sessionId} 在 php\api\app\Services\PyApi\WebSocketService.php 中创建）
            $sessionKey = "websocket_auth_{$sessionId}";
            $sessionData = Cache::get($sessionKey);

            // 🔍 添加详细的调试信息
            // $this->info("🔍 Modern WebSocket Session验证调试信息:");
            // $this->info("   - Session ID: {$sessionId}");
            // $this->info("   - Cache Key: {$sessionKey}");
            // $this->info("   - Session Data: " . ($sessionData ? json_encode($sessionData) : 'null'));

            if (!$sessionData) {
                $this->error("❌ Modern Session验证失败: Session不存在或已过期");
                $this->sendErrorAndClose($server, $fd, 401, 'Session不存在或已过期');
                return;
            }

            if (!isset($sessionData['user_id']) || empty($sessionData['user_id'])) {
                $this->sendErrorAndClose($server, $fd, 401, 'Session中缺少用户信息');
                return;
            }

            // 验证Session是否过期
            if (isset($sessionData['expires_at'])) {
                $expiresAt = Carbon::parse($sessionData['expires_at']);
                if ($expiresAt->isPast()) {
                    $this->sendErrorAndClose($server, $fd, 401, 'Session已过期');
                    return;
                }
            }

            // 获取已验证的用户ID
            $userId = $sessionData['user_id'];
        }

        // 🚀 新增：智能session_id验证逻辑
        $validationResult = $this->validateSessionForConnection($sessionId, $userId, $server, $fd);

        if (!$validationResult['success']) {
            // 验证失败，已经在validateSessionForConnection中处理了响应
            return;
        }

        $session = $validationResult['session'];

        // 更新会话状态和连接时间（注意：不在此处更新last_ping_at，让心跳检查逻辑有意义）
        $session->status = 'connected';
        $session->connected_at = Carbon::now();
        // 🔧 修复：不在连接时立即更新last_ping_at，保持原有的心跳时间用于检查逻辑
        $session->save();

        // 存储连接映射
        Cache::put("websocket_fd_{$fd}", $sessionId, 3600);
        Cache::put("websocket_session_{$sessionId}", $fd, 3600);

        $this->info("Client connected: fd={$fd}, session={$sessionId}, user={$userId}");
        Log::info('WebSocket客户端连接成功', [
            'fd' => $fd,
            'session_id' => $sessionId,
            'user_id' => $session->user_id,
            'connected_at' => $session->connected_at,
            'last_ping_at' => $session->last_ping_at
        ]);

        // 发送连接成功消息
        $server->push($fd, json_encode([
            'event' => 'connected',
            'data' => [
                'session_id' => $sessionId,
                'server_time' => Carbon::now()->format('c')
            ]
        ]));
    }

    /**
     * 现代模式：处理消息
     */
    private function handleModernMessage($server, $frame)
    {
        $fd = $frame->fd;
        $data = $frame->data;

        // 添加详细的调试日志
        Log::info('🔵 WebSocket收到消息', [
            'fd' => $fd,
            'data' => $data,
            'timestamp' => Carbon::now()->format('c')
        ]);

        try {
            $message = json_decode($data, true);
            if (!$message) {
                Log::warning('❌ JSON解析失败', ['fd' => $fd, 'data' => $data]);
                return;
            }

            Log::info('✅ JSON解析成功', ['fd' => $fd, 'message' => $message]);

            $sessionId = Cache::get("websocket_fd_{$fd}");
            Log::info('🔍 查找会话ID', ['fd' => $fd, 'session_id' => $sessionId]);

            if (!$sessionId) {
                Log::warning('❌ 会话ID不存在，关闭连接', ['fd' => $fd]);
                $server->close($fd);
                return;
            }

            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            Log::info('🔍 查找会话记录', ['session_id' => $sessionId, 'found' => !!$session]);

            if (!$session) {
                Log::warning('❌ 会话记录不存在，关闭连接', ['session_id' => $sessionId, 'fd' => $fd]);
                $server->close($fd);
                return;
            }

            $messageType = $message['type'] ?? '';
            Log::info('📝 处理消息类型', ['type' => $messageType, 'session_id' => $sessionId]);

            // 处理不同类型的消息
            switch ($messageType) {
                case 'ping':
                    Log::info('💓 处理心跳消息', ['session_id' => $sessionId, 'fd' => $fd]);
                    $this->handleModernPing($server, $fd, $session);
                    break;

                case 'subscribe':
                    $this->handleModernSubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                case 'unsubscribe':
                    $this->handleModernUnsubscribe($server, $fd, $session, $message['events'] ?? []);
                    break;

                default:
                    Log::info('❓ 未知消息类型', ['type' => $messageType, 'session_id' => $sessionId]);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket消息处理失败', [
                'fd' => $fd,
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 🚀 智能session_id验证逻辑
     *
     * @param string $sessionId 会话ID
     * @param int $userId 用户ID
     * @param $server WebSocket服务器实例
     * @param int $fd 文件描述符
     * @return array 验证结果
     */
    private function validateSessionForConnection(string $sessionId, int $userId, $server, int $fd): array
    {
        // 1. 查找session_id对应的会话记录
        $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();

        if (!$session) {
            // 3. session_id不存在，按正常流程建立连接
            Log::info('WebSocket新会话连接', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'fd' => $fd,
                'reason' => 'session_id不存在，创建新连接'
            ]);

            // 创建新的会话记录（首次连接不设置last_ping_at，保持时序逻辑正确）
            $session = \App\Models\WebSocketSession::create([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => 'python_tool', // 默认类型
                'business_type' => 'unknown', // 待更新
                'status' => 'pending',
                'connection_ip' => $this->getClientIp($fd),
                'connected_at' => null,
                'last_ping_at' => null, // 🔧 修复：首次连接不设置心跳时间，保持时序逻辑
                'expires_at' => \Carbon\Carbon::now()->addMinutes(5),
                'message_count' => 0
            ]);

            return ['success' => true, 'session' => $session];
        }

        // session_id存在，进行进一步验证
        if ($session->user_id == $userId) {
            // 1. session_id归属当前用户
            if ($session->status === 'connected') {
                // 🔧 修复：使用正确的缓存键获取文件描述符
                $existingFd = \Illuminate\Support\Facades\Cache::get("websocket_session_{$sessionId}");

                if ($existingFd && $server->exist($existingFd)) {
                    // 已有活跃连接，推送提示信息
                    $this->sendActiveConnectionNotice($server, $fd, $sessionId);
                    return ['success' => false, 'reason' => 'already_connected'];
                } else {
                    // 会话记录显示连接但实际连接不存在，允许重新连接
                    Log::info('WebSocket会话重连', [
                        'session_id' => $sessionId,
                        'user_id' => $userId,
                        'fd' => $fd,
                        'existing_fd' => $existingFd,
                        'reason' => '会话记录存在但连接已断开，允许重连'
                    ]);
                    return ['success' => true, 'session' => $session];
                }
            } else {
                // 会话存在但未连接，允许连接
                Log::info('WebSocket会话恢复连接', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'fd' => $fd,
                    'old_status' => $session->status,
                    'reason' => '会话存在但未连接，恢复连接'
                ]);
                return ['success' => true, 'session' => $session];
            }
        } else {
            // 2. session_id不归属当前用户，返回401错误
            $this->sendUnauthorizedError($server, $fd, $sessionId, $userId, $session->user_id);
            return ['success' => false, 'reason' => 'unauthorized'];
        }
    }

    /**
     * 发送活跃连接通知
     */
    private function sendActiveConnectionNotice($server, int $fd, string $sessionId): void
    {
        $message = [
            'type' => 'connection_notice',
            'code' => 200,
            'message' => '当前session_id已建立连接，处在活跃状态',
            'data' => [
                'session_id' => $sessionId,
                'status' => 'already_connected',
                'timestamp' => \Carbon\Carbon::now()->format('c')
            ]
        ];

        $server->push($fd, json_encode($message));

        Log::info('WebSocket发送活跃连接通知', [
            'session_id' => $sessionId,
            'fd' => $fd,
            'message' => $message['message']
        ]);

        // 发送通知后关闭新连接
        $server->close($fd);
    }

    /**
     * 发送未授权错误
     */
    private function sendUnauthorizedError($server, int $fd, string $sessionId, int $requestUserId, int $sessionUserId): void
    {
        $message = [
            'type' => 'error',
            'code' => 401,
            'message' => 'session_id不归属当前用户，连接被拒绝',
            'data' => [
                'session_id' => $sessionId,
                'request_user_id' => $requestUserId,
                'session_owner_id' => $sessionUserId,
                'timestamp' => \Carbon\Carbon::now()->format('c')
            ]
        ];

        $server->push($fd, json_encode($message));

        Log::warning('WebSocket未授权连接尝试', [
            'session_id' => $sessionId,
            'request_user_id' => $requestUserId,
            'session_owner_id' => $sessionUserId,
            'fd' => $fd,
            'message' => $message['message']
        ]);

        // 发送错误后关闭连接
        $server->close($fd);
    }

    /**
     * 发送错误信息并关闭连接
     */
    private function sendErrorAndClose($server, int $fd, int $code, string $message): void
    {
        $errorMessage = [
            'type' => 'error',
            'code' => $code,
            'message' => $message,
            'data' => [
                'timestamp' => \Carbon\Carbon::now()->format('c')
            ]
        ];

        $server->push($fd, json_encode($errorMessage));
        $server->close($fd);

        Log::warning('WebSocket连接错误', [
            'fd' => $fd,
            'code' => $code,
            'message' => $message
        ]);
    }



    /**
     * 获取客户端IP地址
     */
    private function getClientIp(int $fd): string
    {
        // 这里可以根据实际情况获取客户端IP
        return '127.0.0.1';
    }

    /**
     * 现代模式：处理连接关闭
     */
    private function handleModernClose($server, $fd)
    {
        $sessionId = Cache::get("websocket_fd_{$fd}");

        if ($sessionId) {
            $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
            if ($session) {
                $session->disconnect('客户端断开连接');
            }

            // 清理缓存
            Cache::forget("websocket_fd_{$fd}");
            Cache::forget("websocket_session_{$sessionId}");
        }

        $this->info("Client disconnected: fd={$fd}");
        Log::info('WebSocket客户端断开', ['fd' => $fd, 'session_id' => $sessionId]);
    }

    /**
     * 现代模式：处理心跳
     */
    private function handleModernPing($server, $fd, $session)
    {
        Log::info('💓 开始处理心跳', [
            'session_id' => $session->session_id,
            'fd' => $fd,
            'user_id' => $session->user_id,
            'current_last_ping_at' => $session->last_ping_at,
            'timestamp' => Carbon::now()->format('c')
        ]);

        try {
            // 🔧 优化：不再立即更新数据库，只记录日志
            Log::info('✅ 现代模式心跳处理 (内存表已更新)', [
                'session_id' => $session->session_id,
                'user_id' => $session->user_id,
                'fd' => $fd,
                'timestamp' => Carbon::now()->format('c')
            ]);

            // 发送pong响应
            $pongMessage = json_encode([
                'event' => 'pong',
                'data' => [
                    'server_time' => Carbon::now()->format('c')
                ]
            ]);

            $server->push($fd, $pongMessage);

            Log::info('🟢 发送pong响应成功', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'message' => $pongMessage,
                'timestamp' => Carbon::now()->format('c')
            ]);

        } catch (\Exception $e) {
            Log::error('❌ 心跳处理失败', [
                'session_id' => $session->session_id,
                'fd' => $fd,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => Carbon::now()->format('c')
            ]);
        }
    }

    /**
     * 现代模式：处理订阅事件
     */
    private function handleModernSubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->subscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'subscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 现代模式：处理取消订阅事件
     */
    private function handleModernUnsubscribe($server, $fd, $session, $events)
    {
        foreach ($events as $event) {
            $session->unsubscribeEvent($event);
        }

        $server->push($fd, json_encode([
            'event' => 'unsubscribed',
            'data' => [
                'events' => $events,
                'subscribed_events' => $session->subscribed_events
            ]
        ]));
    }

    /**
     * 检查路径是否为绝对路径
     */
    private function isAbsolutePath($path)
    {
        // Windows: 检查是否以盘符开头 (如 C:\ 或 D:\)
        if (PHP_OS_FAMILY === 'Windows' || strpos(PHP_OS, 'WIN') !== false) {
            return preg_match('/^[a-zA-Z]:[\\\\\/]/', $path);
        }
        // Unix/Linux: 检查是否以 / 开头
        return strpos($path, '/') === 0;
    }

    /**
     * 启动Redis订阅协程
     */
    private function startRedisSubscription($server): void
    {
        try {
            $this->info('🔍 开始启动Redis订阅协程...');
            Log::info('🔍 WebSocket服务器 - 开始启动Redis订阅协程');

            // 使用Laravel Redis门面，它会自动处理连接池和协程兼容性
            $this->info('🔍 使用Laravel Redis门面');
            Log::info('🔍 WebSocket服务器 - 使用Laravel Redis门面');

            $this->info('✅ Redis连接成功，启动订阅协程');
            Log::info('✅ WebSocket服务器 - Redis连接成功，启动订阅协程');

            // 启动协程订阅Redis通道
            Coroutine::create(function () use ($server) {
                $this->subscribeRedisChannelsWithLaravel($server);
            });

        } catch (\Exception $e) {
            $this->error('❌ Redis订阅协程启动失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis订阅协程启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 订阅Redis通道并处理消息
     */
    private function subscribeRedisChannels($redis, $server): void
    {
        try {
            $this->info('🔍 开始订阅Redis通道: websocket:push:*');
            Log::info('🔍 WebSocket服务器 - 开始订阅Redis通道', [
                'pattern' => 'websocket:push:*',
                'redis_client' => get_class($redis)
            ]);

            // 根据Redis客户端类型选择不同的订阅方式
            if ($redis instanceof \Swoole\Coroutine\Redis) {
                // Swoole协程Redis客户端
                $this->subscribeSwooleRedis($redis, $server);
            } else {
                // 标准Redis扩展
                $this->subscribeStandardRedis($redis, $server);
            }

        } catch (\Exception $e) {
            $this->error('❌ Redis通道订阅失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis通道订阅失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 使用Swoole协程Redis客户端订阅
     */
    private function subscribeSwooleRedis($redis, $server): void
    {
        $result = $redis->psubscribe(['websocket:push:*']);
        if (!$result) {
            throw new \Exception("Redis模式订阅失败: " . $redis->errMsg);
        }

        $this->info('✅ Swoole Redis模式订阅成功，开始监听消息...');
        Log::info('✅ WebSocket服务器 - Swoole Redis模式订阅成功');

        // 持续监听Redis消息
        while (true) {
            $message = $redis->recv();
            if ($message === false) {
                Log::error('❌ WebSocket服务器 - Redis接收消息失败', ['error' => $redis->errMsg]);
                break;
            }

            $this->processRedisMessage($message, $server);
        }
    }

    /**
     * 使用标准Redis扩展订阅
     */
    private function subscribeStandardRedis($redis, $server): void
    {
        $this->info('✅ 标准Redis模式订阅成功，使用定时器轮询模式...');
        Log::info('✅ WebSocket服务器 - 标准Redis模式订阅成功，使用定时器轮询');

        // 在Swoole环境中，使用定时器轮询Redis消息而不是阻塞订阅
        // 这样可以避免Redis连接在协程环境中的问题
        \Swoole\Timer::tick(1000, function () use ($redis, $server) {
            try {
                // 检查Redis连接状态
                if (!$redis->ping()) {
                    Log::warning('❌ WebSocket服务器 - Redis连接断开，尝试重连');
                    $redis->connect(
                        config('database.redis.default.host', '127.0.0.1'),
                        config('database.redis.default.port', 6379)
                    );
                    $redis->select(config('database.redis.default.database', 0));
                }

                // 使用BLPOP检查是否有新消息（这里我们改用其他方式）
                // 由于标准Redis扩展的psubscribe在Swoole中有问题，我们使用其他方式

            } catch (\Exception $e) {
                Log::error('❌ WebSocket服务器 - Redis轮询检查失败', [
                    'error' => $e->getMessage()
                ]);
            }
        });

        $this->info('✅ Redis定时器轮询已启动');
        Log::info('✅ WebSocket服务器 - Redis定时器轮询已启动');
    }

    /**
     * 使用Redis列表轮询消息
     */
    private function subscribeRedisChannelsWithLaravel($server): void
    {
        try {
            $this->info('🔍 开始使用Redis列表轮询消息');
            Log::info('🔍 WebSocket服务器 - 开始使用Redis列表轮询消息');

            // 启动定时器轮询Redis列表
            \Swoole\Timer::tick(100, function () use ($server) {
                try {
                    $this->pollRedisQueues($server);
                } catch (\Exception $e) {
                    Log::error('❌ WebSocket服务器 - Redis列表轮询失败', [
                        'error' => $e->getMessage()
                    ]);
                }
            });

            $this->info('✅ Redis列表轮询已启动');
            Log::info('✅ WebSocket服务器 - Redis列表轮询已启动');

        } catch (\Exception $e) {
            $this->error('❌ Redis列表轮询启动失败: ' . $e->getMessage());
            Log::error('❌ WebSocket服务器 - Redis列表轮询启动失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 轮询Redis队列获取消息
     */
    private function pollRedisQueues($server): void
    {
        try {
            // 获取所有活跃的WebSocket会话
            $activeSessions = \App\Models\WebSocketSession::active()->pluck('session_id');

            foreach ($activeSessions as $sessionId) {
                $listKey = "websocket:queue:{$sessionId}";

                // 使用RPOP从列表右侧弹出消息（FIFO）
                $redis = app('redis');
                $message = $redis->rpop($listKey);

                if ($message) {
                    Log::info('🔍 WebSocket服务器 - 从Redis列表获取消息', [
                        'session_id' => $sessionId,
                        'list_key' => $listKey,
                        'message_length' => strlen($message)
                    ]);

                    $this->processRedisMessageData($message, $server);
                }
            }

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 轮询Redis队列失败', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 处理Redis消息（Swoole格式）
     */
    private function processRedisMessage($message, $server): void
    {
        try {
            // 处理订阅消息
            if (is_array($message) && count($message) >= 4) {
                $type = $message[0];      // 消息类型 (pmessage)
                $pattern = $message[1];   // 订阅模式
                $channel = $message[2];   // 实际通道
                $data = $message[3];      // 消息内容

                if ($type === 'pmessage') {
                    Log::info('🔍 WebSocket服务器 - 收到Redis消息', [
                        'pattern' => $pattern,
                        'channel' => $channel,
                        'message_length' => strlen($data)
                    ]);

                    $this->processRedisMessageData($data, $server);
                }
            }
        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 处理Redis消息失败', [
                'message' => $message,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理Redis消息数据
     */
    private function processRedisMessageData($data, $server): void
    {
        try {
            $messageData = json_decode($data, true);
            if (!$messageData) {
                Log::warning('❌ WebSocket服务器 - Redis消息解析失败', ['message' => $data]);
                return;
            }

            $sessionId = $messageData['session_id'] ?? null;
            $messageContent = $messageData['message'] ?? null;

            if (!$sessionId || !$messageContent) {
                Log::warning('❌ WebSocket服务器 - Redis消息格式错误', ['data' => $messageData]);
                return;
            }

            Log::info('✅ WebSocket服务器 - 准备推送消息到WebSocket连接', [
                'session_id' => $sessionId,
                'message_type' => $messageContent['event'] ?? 'unknown'
            ]);

            // 查找对应的WebSocket连接并推送消息
            $this->pushToWebSocketConnection($server, $sessionId, $messageContent);

        } catch (\Exception $e) {
            Log::error('❌ WebSocket服务器 - 处理Redis消息数据失败', [
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 推送消息到WebSocket连接
     */
    private function pushToWebSocketConnection($server, string $sessionId, array $message): void
    {
        try {
            // 从缓存获取连接ID
            $connectionId = Cache::get("websocket_session_{$sessionId}");

            if (!$connectionId || !$server->exist($connectionId)) {
                Log::warning('WebSocket连接不存在', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);

                // 尝试从数据库获取会话信息并清理无效会话
                $session = \App\Models\WebSocketSession::where('session_id', $sessionId)->first();
                if ($session && $session->status === 'connected') {
                    $session->status = 'disconnected';
                    $session->disconnect_reason = '连接已断开';
                    $session->disconnected_at = Carbon::now();
                    $session->save();

                    Log::info('清理无效WebSocket会话', ['session_id' => $sessionId]);
                }
                return;
            }

            // 推送消息到WebSocket连接
            $success = $server->push($connectionId, json_encode($message));

            if ($success) {
                Log::info('WebSocket消息推送成功', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId,
                    'event' => $message['event'] ?? 'unknown'
                ]);
            } else {
                Log::warning('WebSocket消息推送失败', [
                    'session_id' => $sessionId,
                    'connection_id' => $connectionId
                ]);
            }

        } catch (\Exception $e) {
            Log::error('推送WebSocket消息异常', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }
}