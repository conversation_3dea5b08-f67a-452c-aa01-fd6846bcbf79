<?php

namespace App\Services\PyApi;

use App\Helpers\LogCheckHelper;
use App\Helpers\EventTypeHelper;
use App\Enums\ApiCodeEnum;
use App\Models\WebSocketSession;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * WebSocket服务
 */
class WebSocketService
{
    /**
     * 🚀 批量任务专用：认证多个WebSocket连接
     * 用于批量生成分镜图片等需要多连接并行处理的场景
     */
    public function authenticateBatchConnections(
        int $userId,
        string $clientType,
        string $businessType,
        int $connectionCount,
        string $connectionIp,
        ?string $userAgent = null,
        ?string $clientVersion = null,
        array $connectionInfo = []
    ): array {
        try {
            DB::beginTransaction();

            // 验证客户端类型
            if (!$this->isValidClientType($clientType)) {
                return [
                    'code' => ApiCodeEnum::INVALID_CLIENT_TYPE,
                    'message' => '不支持的客户端类型',
                    'data' => []
                ];
            }

            // 验证业务类型并获取映射
            $taskType = config('ai.business_type_mappings.frontend_to_backend.' . $businessType);
            if (!$taskType) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '不支持的业务类型',
                    'data' => ['business_type' => $businessType]
                ];
            }

            // 验证批量连接数量限制
            if ($connectionCount < 1 || $connectionCount > 10) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '批量连接数量必须在1-10之间',
                    'data' => ['connection_count' => $connectionCount]
                ];
            }

            Log::info('WebSocket批量连接认证开始', [
                'user_id' => $userId,
                'client_type' => $clientType,
                'business_type' => $businessType,
                'backend_business_type' => $taskType,
                'connection_count' => $connectionCount,
                'connection_ip' => $connectionIp
            ]);

            // 检查现有连接并清理
            $existingSessions = WebSocketSession::byUser($userId)
                ->byClientType($clientType)
                ->byBusinessType($taskType)
                ->active()
                ->get();

            foreach ($existingSessions as $existingSession) {
                $existingSession->disconnect('批量任务开始，清理旧连接');
                Log::info('WebSocket批量任务清理旧连接', [
                    'old_session_id' => $existingSession->session_id,
                    'user_id' => $userId,
                    'business_type' => $taskType
                ]);
            }

            // 创建多个连接（使用新的多连接支持）
            $connections = [];
            $batchId = "batch_" . time() . "_" . $userId;

            for ($i = 1; $i <= $connectionCount; $i++) {
                // 使用authenticateConnection方法创建连接，启用多连接模式
                $connectionResult = $this->authenticateConnection(
                    $userId,
                    $clientType,
                    $businessType,
                    $connectionIp,
                    $userAgent,
                    $clientVersion,
                    ['batch_id' => $batchId, 'batch_index' => $i],
                    true  // 启用多连接模式
                );

                if ($connectionResult['code'] !== ApiCodeEnum::SUCCESS) {
                    Log::error('WebSocket批量连接创建失败', [
                        'batch_index' => $i,
                        'user_id' => $userId,
                        'business_type' => $businessType,
                        'error' => $connectionResult['message']
                    ]);
                    continue;
                }

                $sessionId = $connectionResult['data']['session_id'];

                // 更新会话记录，添加批量标识
                $session = WebSocketSession::where('session_id', $sessionId)->first();
                if ($session) {
                    $session->update([
                        'batch_id' => $batchId,
                        'batch_index' => $i
                    ]);
                }

                $connections[] = [
                    'session_id' => $sessionId,
                    'batch_index' => $i,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'expires_at' => $session->expires_at->format('c')
                ];

                Log::info('WebSocket批量连接创建', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'business_type' => $taskType,
                    'batch_index' => $i,
                    'total_connections' => $connectionCount
                ]);
            }

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "批量WebSocket连接认证成功，创建了{$connectionCount}个连接",
                'data' => [
                    'connections' => $connections,
                    'connection_count' => $connectionCount,
                    'business_type' => $businessType,
                    'backend_business_type' => $taskType,
                    'batch_mode' => true,
                    'heartbeat_interval' => 30,
                    'max_idle_time' => 300,
                    'usage_tips' => [
                        '每个连接可以独立处理任务',
                        '建议将任务均匀分配到各个连接',
                        '所有连接共享相同的业务类型和权限'
                    ]
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('WebSocket批量连接认证失败', [
                'user_id' => $userId,
                'business_type' => $businessType,
                'connection_count' => $connectionCount,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::INTERNAL_ERROR,
                'message' => 'WebSocket批量连接认证失败',
                'data' => []
            ];
        }
    }

    /**
     * 认证WebSocket连接
     */
    public function authenticateConnection(
        int $userId,
        string $clientType,
        string $businessType,
        string $connectionIp,
        ?string $userAgent = null,
        ?string $clientVersion = null,
        array $connectionInfo = [],
        bool $allowMultipleConnections = false  // 🚀 新增：是否允许同类型多连接
    ): array
    {
        try {
            DB::beginTransaction();

            // 验证客户端类型
            if (!$this->isValidClientType($clientType)) {
                // 特别处理web_browser类型，返回403禁止访问
                if ($clientType === WebSocketSession::CLIENT_TYPE_WEB_BROWSER) {
                    return [
                        'code' => ApiCodeEnum::FORBIDDEN,
                        'message' => 'Web浏览器不支持WebSocket连接',
                        'data' => []
                    ];
                }

                return [
                    'code' => ApiCodeEnum::INVALID_PARAMS,
                    'message' => '无效的客户端类型',
                    'data' => []
                ];
            }

            // 🚀 优化：基于业务类型的智能连接限制
            if ($clientType === WebSocketSession::CLIENT_TYPE_PYTHON_TOOL) {
                $pythonConnections = WebSocketSession::byUser($userId)
                    ->pythonTool()
                    ->active()
                    ->get();

                // 统计不同业务类型的连接数
                $businessTypeCount = $pythonConnections->groupBy('business_type')->count();
                $totalConnections = $pythonConnections->count();

                // 获取所有支持的业务类型数量
                $maxBusinessTypes = count(config('ai.business_type_mappings.frontend_to_backend', []));

                Log::info('WebSocket连接限制检查', [
                    'user_id' => $userId,
                    'client_type' => $clientType,
                    'current_connections' => $totalConnections,
                    'business_types_used' => $businessTypeCount,
                    'max_business_types' => $maxBusinessTypes,
                    'existing_business_types' => $pythonConnections->pluck('business_type')->unique()->values()->toArray()
                ]);

                // 🔧 新限制策略：
                // 1. 每个业务类型最多1个连接（通过连接复用保证）
                // 2. 总连接数不超过支持的业务类型数量
                // 3. 额外的安全限制：最多20个连接（防止配置错误）
                $maxConnections = min($maxBusinessTypes, 20);

                if ($totalConnections >= $maxConnections) {
                    return [
                        'code' => ApiCodeEnum::CONNECTION_LIMIT,
                        'message' => "用户连接数已达上限（{$totalConnections}/{$maxConnections}）",
                        'data' => [
                            'current_connections' => $totalConnections,
                            'max_connections' => $maxConnections,
                            'business_types_used' => $businessTypeCount,
                            'suggestion' => '请关闭不需要的业务类型连接，或等待自动超时清理'
                        ]
                    ];
                }

                // 注意：业务类型检查将在后面的智能连接管理中处理
                Log::info('WebSocket连接限制检查通过', [
                    'user_id' => $userId,
                    'client_type' => $clientType,
                    'current_connections' => $totalConnections,
                    'max_connections' => $maxConnections,
                    'frontend_business_type' => $businessType,
                    'status' => 'allowed'
                ]);
            }

            // 生成会话ID
            $sessionId = 'ws_' . Str::random(32);

            // 🚨 修复：将前端业务类型映射为数据库存储的任务类型
            $taskType = config('ai.business_type_mappings.frontend_to_backend.' . $businessType);
            if (!$taskType) {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => '不支持的业务类型',
                    'data' => ['business_type' => $businessType]
                ];
            }

            Log::info('WebSocket业务类型映射', [
                'frontend_business_type' => $businessType,
                'mapped_task_type' => $taskType,
                'user_id' => $userId,
                'session_id' => $sessionId
            ]);

            // 🚀 优化：智能连接管理 - 相同业务类型复用连接，不同业务类型切换连接
            $existingSessions = WebSocketSession::byUser($userId)
                ->byClientType($clientType)
                ->active()
                ->get();

            Log::info('WebSocket连接管理检查', [
                'user_id' => $userId,
                'client_type' => $clientType,
                'target_business_type' => $taskType,
                'existing_sessions_count' => $existingSessions->count(),
                'existing_sessions' => $existingSessions->map(function($session) {
                    return [
                        'session_id' => $session->session_id,
                        'business_type' => $session->business_type,
                        'status' => $session->status,
                        'connected_at' => $session->connected_at->format('c')
                    ];
                })->toArray()
            ]);

            // 🚀 直接支持多连接：同用户同业务类型允许多个连接
            $existingSameTypeCount = $existingSessions->where('business_type', $taskType)->count();

            Log::info('WebSocket多连接支持：创建新连接', [
                'user_id' => $userId,
                'client_type' => $clientType,
                'business_type' => $taskType,
                'frontend_business_type' => $businessType,
                'existing_same_type_connections' => $existingSameTypeCount,
                'allow_multiple_connections' => $allowMultipleConnections,
                'reason' => '直接支持同用户同业务类型多连接'
            ]);

            // 不同业务类型：断开其他业务类型的连接
            $differentBusinessTypeSessions = $existingSessions->where('business_type', '!=', $taskType);
            $disconnectedCount = 0;

            foreach ($differentBusinessTypeSessions as $existingSession) {
                try {
                    $existingSession->disconnect('业务类型切换，自动断开旧会话');
                    $disconnectedCount++;

                    Log::info('WebSocket业务类型切换断开', [
                        'old_session_id' => $existingSession->session_id,
                        'old_business_type' => $existingSession->business_type,
                        'new_business_type' => $taskType,
                        'user_id' => $userId,
                        'client_type' => $clientType,
                        'old_connection_duration_minutes' => $existingSession->connected_at->diffInMinutes(Carbon::now()),
                        'reason' => '业务类型切换'
                    ]);
                } catch (\Exception $e) {
                    Log::error('WebSocket旧会话断开失败', [
                        'session_id' => $existingSession->session_id,
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($disconnectedCount > 0) {
                Log::info('WebSocket业务类型切换完成', [
                    'user_id' => $userId,
                    'client_type' => $clientType,
                    'from_business_types' => $differentBusinessTypeSessions->pluck('business_type')->toArray(),
                    'to_business_type' => $taskType,
                    'disconnected_sessions_count' => $disconnectedCount
                ]);
            }

            // 创建WebSocket会话 - 存储映射后的任务类型（不设置心跳时间，保持时序逻辑）
            $session = WebSocketSession::create([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => $clientType,
                'business_type' => $taskType,  // 🚨 修复：存储映射后的值
                'client_version' => $clientVersion,
                'connection_ip' => $connectionIp,
                'user_agent' => $userAgent,
                'status' => 'pending', // 🔧 修复：初始状态为pending，等待连接建立
                'connection_info' => $connectionInfo,
                'connected_at' => null, // 🔧 修复：连接建立时才设置
                'last_ping_at' => null  // 🔧 修复：首次心跳时才设置，保持时序逻辑
            ]);

            // 根据业务类型设置默认订阅事件 - 使用映射后的任务类型
            $defaultEvents = $this->getDefaultSubscribedEvents($clientType, $taskType);
            $session->subscribed_events = $defaultEvents;
            $session->save();

            // 🔧 修复：将Session数据存储到缓存，供WebSocket服务器验证使用
            // 使用不同的缓存键避免与文件描述符存储冲突
            $expiresAt = Carbon::now()->addMinutes(5); // 5分钟后过期
            $sessionCacheKey = "websocket_auth_{$sessionId}";
            $sessionCacheData = [
                'user_id' => $userId,
                'client_type' => $clientType,
                'business_type' => $taskType,
                'connection_ip' => $connectionIp,
                'user_agent' => $userAgent,
                'created_at' => Carbon::now()->format('c'),
                'expires_at' => $expiresAt->format('c'),
                'supported_events' => $defaultEvents
            ];

            // 存储到缓存，过期时间为5分钟
            Cache::put($sessionCacheKey, $sessionCacheData, 300);

            DB::commit();

            Log::info('WebSocket连接认证成功', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'client_type' => $clientType,
                'connection_ip' => $connectionIp,
                'cache_key' => $sessionCacheKey,
                'expires_at' => $expiresAt->format('c')
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'WebSocket认证成功',
                'data' => [
                    'session_id' => $sessionId,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'expires_at' => $expiresAt->format('c'),
                    'supported_events' => $defaultEvents,
                    'heartbeat_interval' => 30, // 心跳间隔（秒）
                    'max_idle_time' => 300 // 最大空闲时间（秒）
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'client_type' => $clientType,
                'connection_ip' => $connectionIp,
                'user_agent' => $userAgent,
                'client_version' => $clientVersion,
            ];

            Log::error('WebSocket连接认证失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'WebSocket连接认证失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户会话列表
     */
    public function getUserSessions(int $userId, ?string $status = null, ?string $clientType = null): array
    {
        try {
            $query = WebSocketSession::byUser($userId);

            if ($status) {
                $query->byStatus($status);
            }

            if ($clientType) {
                $query->byClientType($clientType);
            }

            $sessions = $query->orderBy('connected_at', 'desc')->get();

            $sessionsData = $sessions->map(function ($session) {
                return [
                    'session_id' => $session->session_id,
                    'client_type' => $session->client_type,
                    'client_version' => $session->client_version,
                    'status' => $session->status,
                    'connection_ip' => $session->connection_ip,
                    'subscribed_events' => $session->subscribed_events ?? [],
                    'connected_at' => $session->connected_at->format('Y-m-d H:i:s'),
                    'last_ping_at' => $session->last_ping_at?->format('Y-m-d H:i:s'),
                    'disconnected_at' => $session->disconnected_at?->format('Y-m-d H:i:s'),
                    'message_count' => $session->message_count,
                    'connection_duration' => $session->getConnectionDuration(),
                    'is_active' => $session->isActive()
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => $sessionsData->toArray()
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'status' => $status,
                'client_type' => $clientType,
            ];

            Log::error('获取用户会话列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取会话列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 断开会话连接
     */
    public function disconnectSession(string $sessionId, int $userId, string $reason = '用户主动断开'): array
    {
        try {
            $session = WebSocketSession::where('session_id', $sessionId)
                ->where('user_id', $userId)
                ->first();

            if (!$session) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '会话不存在',
                    'data' => []
                ];
            }

            if ($session->status !== WebSocketSession::STATUS_CONNECTED) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '会话已断开',
                    'data' => []
                ];
            }

            $session->disconnect($reason);

            // 通知WebSocket服务器断开连接
            $this->notifyServerDisconnect($sessionId);

            Log::info('WebSocket会话断开成功', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'reason' => $reason
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '连接已断开',
                'data' => [
                    'session_id' => $sessionId,
                    'disconnected_at' => $session->disconnected_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'reason' => $reason,
            ];

            Log::error('断开WebSocket会话失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '断开连接失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取服务器状态
     */
    public function getServerStatus(): array
    {
        try {
            $cacheKey = 'websocket_server_status';
            $cached = Cache::get($cacheKey);
            
            if ($cached) {
                return $cached;
            }

            // 统计连接数据
            $totalConnections = WebSocketSession::count();
            $activeConnections = WebSocketSession::active()->count();
            $pythonConnections = WebSocketSession::active()->pythonTool()->count();
            $webConnections = WebSocketSession::active()->byClientType('web_browser')->count();

            // 计算服务器运行时间（模拟）
            $serverStartTime = Cache::get('websocket_server_start_time', Carbon::now()->subHours(2));
            $uptime = $serverStartTime->diffForHumans(Carbon::now(), true);

            // 统计消息数量
            $totalMessages = WebSocketSession::sum('message_count');

            $result = [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'server_status' => 'running',
                    'total_connections' => $totalConnections,
                    'active_connections' => $activeConnections,
                    'python_tool_connections' => $pythonConnections,
                    'web_browser_connections' => $webConnections,
                    'server_uptime' => $uptime,
                    'total_messages_sent' => $totalMessages,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'last_check' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ];

            // 缓存1分钟
            Cache::put($cacheKey, $result, 60);

            return $result;

        } catch (\Exception $e) {
            $error_context = [];

            Log::error('获取WebSocket服务器状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取服务器状态失败',
                'data' => []
            ];
        }
    }

    /**
     * 推送消息到指定会话
     */
    public function pushMessage(string $sessionId, string $eventType, array $data): array
    {
        try {
            $session = WebSocketSession::where('session_id', $sessionId)->first();

            if (!$session || !$session->isActive()) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '会话不存在或已失效',
                    'data' => [
                        'session_id' => $sessionId,
                        'is_active' => false
                    ]
                ];
            }

            if (!$session->isSubscribedTo($eventType)) {
                // 🔧 自动订阅机制：如果是EventTypeHelper定义的标准事件类型，自动订阅
                if ($this->isStandardEventType($eventType)) {
                    Log::info('WebSocket自动订阅标准事件类型', [
                        'session_id' => $sessionId,
                        'event_type' => $eventType,
                        'user_id' => $session->user_id,
                        'business_type' => $session->business_type
                    ]);

                    try {
                        $session->subscribeEvent($eventType);
                    } catch (\Exception $e) {
                        Log::error('WebSocket自动订阅失败', [
                            'session_id' => $sessionId,
                            'event_type' => $eventType,
                            'error' => $e->getMessage()
                        ]);

                        return [
                            'code' => ApiCodeEnum::ERROR,
                            'message' => '自动订阅事件类型失败',
                            'data' => [
                                'session_id' => $sessionId,
                                'event_type' => $eventType,
                                'error' => $e->getMessage()
                            ]
                        ];
                    }
                } else {
                    return [
                        'code' => ApiCodeEnum::VALIDATION_ERROR,
                        'message' => '会话未订阅此事件类型',
                        'data' => [
                            'session_id' => $sessionId,
                            'event_type' => $eventType,
                            'subscribed' => false
                        ]
                    ];
                }
            }

            $message = [
                'event' => $eventType,
                'data' => $data,
                'timestamp' => Carbon::now()->format('c'),
                'session_id' => $sessionId
            ];

            // 这里应该调用实际的WebSocket服务器推送消息
            // 目前模拟推送成功
            $this->simulatePushMessage($sessionId, $message);

            $session->incrementMessageCount();

            Log::info('WebSocket消息推送成功', [
                'session_id' => $sessionId,
                'event_type' => $eventType,
                'data_size' => strlen(json_encode($data))
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'WebSocket消息推送成功',
                'data' => [
                    'session_id' => $sessionId,
                    'event_type' => $eventType,
                    'message_count' => $session->message_count
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'session_id' => $sessionId,
                'event_type' => $eventType,
                'data_size' => strlen(json_encode($data))
            ];

            Log::error('WebSocket消息推送失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => 'WebSocket消息推送异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 推送消息到用户的所有活跃会话
     */
    public function pushToUser(int $userId, string $eventType, array $data): array
    {
        try {
            $sessions = WebSocketSession::byUser($userId)->active()->get();
            $successCount = 0;
            $totalSessions = $sessions->count();

            foreach ($sessions as $session) {
                $pushResult = $this->pushMessage($session->session_id, $eventType, $data);
                if ($pushResult['code'] === ApiCodeEnum::SUCCESS) {
                    $successCount++;
                }
            }

            if ($successCount > 0) {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '推送消息到用户成功',
                    'data' => [
                        'user_id' => $userId,
                        'success_count' => $successCount,
                        'total_sessions' => $totalSessions,
                        'event_type' => $eventType
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::ERROR,
                    'message' => '推送消息到用户失败，没有成功的会话',
                    'data' => [
                        'user_id' => $userId,
                        'success_count' => 0,
                        'total_sessions' => $totalSessions,
                        'event_type' => $eventType
                    ]
                ];
            }

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'event_type' => $eventType,
                'data_size' => strlen(json_encode($data))
            ];

            Log::error('推送消息到用户失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '推送消息到用户异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 清理超时连接
     */
    public function cleanupTimeoutSessions(): int
    {
        try {
            $timeoutSessions = WebSocketSession::timeout()->get();
            $cleanedCount = 0;

            foreach ($timeoutSessions as $session) {
                $session->markAsTimeout();
                $cleanedCount++;
            }

            Log::info('清理超时WebSocket连接', [
                'cleaned_count' => $cleanedCount
            ]);

            return $cleanedCount;

        } catch (\Exception $e) {
            $error_context = [];

            Log::error('清理超时WebSocket连接失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 0;
        }
    }

    /**
     * 验证客户端类型是否有效
     * 根据index.mdc规范，WebSocket仅限Py视频创作工具和移动应用使用
     * 为了测试目的，临时允许web_browser类型（仅在开发环境）
     */
    private function isValidClientType(string $clientType): bool
    {
        $allowedTypes = [
            WebSocketSession::CLIENT_TYPE_PYTHON_TOOL,
            WebSocketSession::CLIENT_TYPE_MOBILE_APP
        ];

        // 开发环境下允许web_browser用于测试
        if (app()->environment(['local', 'development', 'testing'])) {
            $allowedTypes[] = WebSocketSession::CLIENT_TYPE_WEB_BROWSER;
        }

        return in_array($clientType, $allowedTypes);
    }

    /**
     * 获取默认订阅事件（根据客户端类型和业务类型）
     */
    private function getDefaultSubscribedEvents(string $clientType, string $businessType = ''): array
    {
        // 基础事件（所有业务类型都支持）
        $baseEvents = [
            WebSocketSession::EVENT_AI_GENERATION_PROGRESS,
            WebSocketSession::EVENT_POINTS_CHANGED
        ];

        // 根据业务类型动态添加对应的完成和失败事件
        if (!empty($businessType)) {
            $completedEvent = EventTypeHelper::getCompletedEventType($businessType);
            $failedEvent = EventTypeHelper::getFailedEventType($businessType);

            if ($completedEvent) {
                $baseEvents[] = $completedEvent;
            }
            if ($failedEvent) {
                $baseEvents[] = $failedEvent;
            }
        }

        // 添加通用的AI生成事件作为后备
        $baseEvents[] = 'ai_generation_completed';
        $baseEvents[] = 'ai_generation_failed';

        // 根据业务类型添加特定的批量处理事件
        switch ($businessType) {
            case 'text_generation_aistory':
                // 分镜脚本生成业务的特殊事件
                $baseEvents[] = 'aistory_batch_progress';
                break;
            case 'text_generation_mystory':
                // 分镜脚本生成业务的特殊事件
                $baseEvents[] = 'mystory_batch_progress';
                break;
            case 'image_generation':
            case 'image_generation_character':
            case 'image_generation_style':
                // 图像生成业务的特殊事件
                $baseEvents[] = 'image_batch_progress';
                break;
        }

        // 去重并返回
        return array_unique($baseEvents);
    }

    /**
     * 获取WebSocket连接URL
     */
    private function getWebSocketUrl(): string
    {
        return config('websocket.url');
    }

    /**
     * 通知服务器断开连接
     */
    private function notifyServerDisconnect(string $sessionId): void
    {
        // 这里应该通知实际的WebSocket服务器断开指定连接
        // 目前模拟通知
        Log::info('通知WebSocket服务器断开连接', ['session_id' => $sessionId]);
    }

    /**
     * 真实推送消息到Redis列表
     */
    private function simulatePushMessage(string $sessionId, array $message): void
    {
        try {
            // 获取会话信息以确保连接活跃
            $session = WebSocketSession::where('session_id', $sessionId)->first();
            if (!$session || !$session->isActive()) {
                $errorMessage = !$session ? 'WebSocket会话不存在' : 'WebSocket会话不活跃';

                Log::warning($errorMessage . '，推送失败', [
                    'session_id' => $sessionId,
                    'session_exists' => $session ? 'yes' : 'no',
                    'session_active' => $session ? ($session->isActive() ? 'yes' : 'no') : 'unknown'
                ]);

                // 抛出异常，让上层方法知道推送失败
                throw new \Exception($errorMessage);
            }

            // 推送消息到Redis列表，WebSocket服务器会轮询此列表
            $listKey = "websocket:queue:{$sessionId}";
            $messageData = [
                'session_id' => $sessionId,
                'user_id' => $session->user_id,
                'business_type' => $session->business_type,
                'message' => $message,
                'timestamp' => time()
            ];

            // 使用Redis列表推送消息
            $redis = app('redis');
            $listLength = $redis->lpush($listKey, json_encode($messageData));

            // 设置列表过期时间（1小时）
            $redis->expire($listKey, 3600);

            Log::info('Redis列表消息推送成功', [
                'list_key' => $listKey,
                'session_id' => $sessionId,
                'user_id' => $session->user_id,
                'business_type' => $session->business_type,
                'message_type' => $message['event'] ?? 'unknown',
                'list_length' => $listLength,
                'data_size' => strlen(json_encode($messageData))
            ]);

        } catch (\Exception $e) {
            Log::error('Redis列表消息推送失败', [
                'session_id' => $sessionId,
                'error' => $e->getMessage(),
                'message' => $message,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 生成WebSocket认证令牌
     * 第2F阶段：新增方法
     */
    public function generateAuthToken(int $userId, array $authParams): array
    {
        try {
            DB::beginTransaction();

            // 生成认证令牌
            $authToken = 'ws_auth_' . Str::random(32);

            // 计算过期时间
            $expiresAt = Carbon::now()->addSeconds($authParams['expires_in']);

            // 存储认证信息到缓存
            $authData = [
                'user_id' => $userId,
                'channel' => $authParams['channel'],
                'permissions' => $authParams['permissions'],
                'client_info' => $authParams['client_info'],
                'created_at' => Carbon::now()->format('c'),
                'expires_at' => $expiresAt->format('c')
            ];

            Cache::put(
                'ws_auth:' . $authToken,
                $authData,
                $authParams['expires_in']
            );

            // 记录认证日志
            Log::info('WebSocket认证令牌生成', [
                'user_id' => $userId,
                'channel' => $authParams['channel'],
                'permissions' => $authParams['permissions'],
                'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
                'client_info' => $authParams['client_info']
            ]);

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'WebSocket认证令牌生成成功',
                'data' => [
                    'auth_token' => $authToken,
                    'websocket_url' => $this->getWebSocketUrl(),
                    'channel' => $authParams['channel'],
                    'user_id' => $userId,
                    'permissions' => $authParams['permissions'],
                    'expires_at' => $expiresAt->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'channel' => $authParams['channel'] ?? null,
                'permissions' => $authParams['permissions'] ?? null,
                'expires_in' => $authParams['expires_in'] ?? null,
                'client_info' => $authParams['client_info'] ?? null
            ];

            Log::error('WebSocket认证令牌生成失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'WebSocket认证令牌生成失败',
                'data' => []
            ];
        }
    }

    /**
     * 验证WebSocket会话
     */
    public function validateSession(string $sessionId, int $userId): array
    {
        try {
            $session = WebSocketSession::where('session_id', $sessionId)
                ->where('user_id', $userId)
                ->first();

            if ($session && $session->isActive()) {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'WebSocket会话验证成功',
                    'data' => [
                        'session_id' => $sessionId,
                        'user_id' => $userId,
                        'is_active' => true
                    ]
                ];
            } else {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'WebSocket会话无效或已过期',
                    'data' => [
                        'session_id' => $sessionId,
                        'user_id' => $userId,
                        'is_active' => false
                    ]
                ];
            }

        } catch (\Exception $e) {
            Log::error('验证WebSocket会话失败', [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::ERROR,
                'message' => '验证WebSocket会话异常：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 检查是否为标准事件类型（EventTypeHelper定义的事件类型）
     */
    private function isStandardEventType(string $eventType): bool
    {
        // 获取所有标准事件类型
        $standardEventTypes = EventTypeHelper::getAllEventTypes();

        return in_array($eventType, $standardEventTypes);
    }
}
