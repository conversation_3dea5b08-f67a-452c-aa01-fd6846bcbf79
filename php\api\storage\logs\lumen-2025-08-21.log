[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":8,"session_id":null} 
[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":7,"session_id":null} 
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"mystory","status":"allowed"} 
[2025-08-21 19:27:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"mystory","mapped_task_type":"text_generation_mystory","user_id":14,"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_mystory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_8iD7o1aMGjp6u8RYWjpsgUgdhgrbrGVB","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:22:34+08:00"},{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"}]} 
[2025-08-21 19:27:17] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","expires_at":"2025-08-21T19:32:17+08:00"} 
[2025-08-21 19:27:33] production.INFO: WebSocket心跳检查 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"last_ping_at":"2025-08-21T19:27:17+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:27:33] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"fd":1,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"},{"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:27:33+08:00"}]} 
[2025-08-21 19:28:19] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":4,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_mystory","text_generation_mystory"],"to_business_type":"text_generation_aistory","disconnected_sessions_count":2} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","expires_at":"2025-08-21T19:33:19+08:00"} 
[2025-08-21 19:28:30] production.INFO: WebSocket心跳检查 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"last_ping_at":"2025-08-21T19:28:19+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:28:30] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"fd":2,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:41] production.INFO: User offline {"user_id":14} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:53] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接认证成功 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","expires_at":"2025-08-21T19:33:53+08:00"} 
[2025-08-21 19:29:06] production.INFO: WebSocket心跳检查 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"last_ping_at":"2025-08-21T19:28:53+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:29:06] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"fd":3,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":257,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":257,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755775753.990739,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:14] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":13.63,"total_stack_cleared":true} 
[2025-08-21 19:29:14] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":0,"timestamp":"2025-08-21T19:29:14+08:00"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptcharacter","status":"allowed"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptcharacter","mapped_task_type":"text_prompt_character","user_id":14,"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_character","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:29:06+08:00"}]} 
[2025-08-21 19:29:42] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_character","frontend_business_type":"promptcharacter","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","old_business_type":"text_generation_aistory","new_business_type":"text_prompt_character","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_aistory"],"to_business_type":"text_prompt_character","disconnected_sessions_count":1} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","expires_at":"2025-08-21T19:34:42+08:00"} 
[2025-08-21 19:30:01] production.INFO: WebSocket心跳检查 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"last_ping_at":"2025-08-21T19:29:42+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:30:01] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"fd":4,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_character"]} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-21T19:30:01+08:00"}]} 
[2025-08-21 19:30:31] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","old_business_type":"text_prompt_character","new_business_type":"text_prompt_style","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_prompt_character"],"to_business_type":"text_prompt_style","disconnected_sessions_count":1} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接认证成功 {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","expires_at":"2025-08-21T19:35:31+08:00"} 
