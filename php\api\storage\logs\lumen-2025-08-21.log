[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":8,"session_id":null} 
[2025-08-21 19:25:30] production.INFO: WebSocket客户端断开 {"fd":7,"session_id":null} 
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:27:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:27:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"mystory","status":"allowed"} 
[2025-08-21 19:27:17] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"mystory","mapped_task_type":"text_generation_mystory","user_id":14,"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_mystory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_8iD7o1aMGjp6u8RYWjpsgUgdhgrbrGVB","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:22:34+08:00"},{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"}]} 
[2025-08-21 19:27:17] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_mystory","frontend_business_type":"mystory","existing_same_type_connections":2,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:27:17] production.INFO: WebSocket连接认证成功 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","expires_at":"2025-08-21T19:32:17+08:00"} 
[2025-08-21 19:27:33] production.INFO: WebSocket心跳检查 {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"last_ping_at":"2025-08-21T19:27:17+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:27:33] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","user_id":14,"fd":1,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":2,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_mystory"]} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":2,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI"} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":2,"existing_sessions":[{"session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:24:08+08:00"},{"session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","business_type":"text_generation_mystory","status":"connected","connected_at":"2025-08-21T19:27:33+08:00"}]} 
[2025-08-21 19:28:19] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_eCihV8I7iMbj2T9QI0K6uLb9wnlzmWkm","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":4,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_Dlo0BzKCLegqXfXUPlCAiawLSuSLGZZD","old_business_type":"text_generation_mystory","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:28:19] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_mystory","text_generation_mystory"],"to_business_type":"text_generation_aistory","disconnected_sessions_count":2} 
[2025-08-21 19:28:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","expires_at":"2025-08-21T19:33:19+08:00"} 
[2025-08-21 19:28:30] production.INFO: WebSocket心跳检查 {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"last_ping_at":"2025-08-21T19:28:19+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:28:30] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_CAnD0fII8gp2ks9tkqj0fo3GSKAlpsPI","user_id":14,"fd":2,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:28:41] production.INFO: User offline {"user_id":14} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:28:53] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 19:28:53] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:28:53] production.INFO: WebSocket连接认证成功 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","expires_at":"2025-08-21T19:33:53+08:00"} 
[2025-08-21 19:29:06] production.INFO: WebSocket心跳检查 {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"last_ping_at":"2025-08-21T19:28:53+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:29:06] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","user_id":14,"fd":3,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":257,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:29:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":257,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755775753.990739,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:29:14] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":13.63,"total_stack_cleared":true} 
[2025-08-21 19:29:14] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":2,"updated_sessions":0,"timestamp":"2025-08-21T19:29:14+08:00"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptcharacter","status":"allowed"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptcharacter","mapped_task_type":"text_prompt_character","user_id":14,"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9"} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_character","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:29:06+08:00"}]} 
[2025-08-21 19:29:42] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_character","frontend_business_type":"promptcharacter","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_nkzUZXEWXBEuH4HFSA35Xa2mfpIALqJo","old_business_type":"text_generation_aistory","new_business_type":"text_prompt_character","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:29:42] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_generation_aistory"],"to_business_type":"text_prompt_character","disconnected_sessions_count":1} 
[2025-08-21 19:29:42] production.INFO: WebSocket连接认证成功 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","expires_at":"2025-08-21T19:34:42+08:00"} 
[2025-08-21 19:30:01] production.INFO: WebSocket心跳检查 {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"last_ping_at":"2025-08-21T19:29:42+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:30:01] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","user_id":14,"fd":4,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_character"]} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"promptstyle","status":"allowed"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"promptstyle","mapped_task_type":"text_prompt_style","user_id":14,"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY"} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_prompt_style","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","business_type":"text_prompt_character","status":"connected","connected_at":"2025-08-21T19:30:01+08:00"}]} 
[2025-08-21 19:30:31] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_prompt_style","frontend_business_type":"promptstyle","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_GhINKqtCMq03VYQ9c6YS252LWPz9BFz9","old_business_type":"text_prompt_character","new_business_type":"text_prompt_style","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":0,"reason":"业务类型切换"} 
[2025-08-21 19:30:31] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_prompt_character"],"to_business_type":"text_prompt_style","disconnected_sessions_count":1} 
[2025-08-21 19:30:31] production.INFO: WebSocket连接认证成功 {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","expires_at":"2025-08-21T19:35:31+08:00"} 
[2025-08-21 19:30:45] production.INFO: WebSocket心跳检查 {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"last_ping_at":"2025-08-21T19:30:31+08:00","minutes_since_last_ping":0,"has_recent_heartbeat":true} 
[2025-08-21 19:30:45] production.INFO: WebSocket会话重连(心跳活跃) {"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","user_id":14,"fd":5,"reason":"检测到最近心跳活动，跳过缓存判断，允许重连"} 
[2025-08-21 19:31:14] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":257,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:31:14] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:31:14] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":257,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755775874.025119,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:31:14] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":7.43,"total_stack_cleared":true} 
[2025-08-21 19:31:14] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":3,"updated_sessions":0,"timestamp":"2025-08-21T19:31:14+08:00"} 
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:33:03] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:33:03] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:33:03] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:33:19] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_prompt_style"]} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr"} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","business_type":"text_prompt_style","status":"connected","connected_at":"2025-08-21T19:30:45+08:00"}]} 
[2025-08-21 19:33:19] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型切换断开 {"old_session_id":"ws_VHiQbUu8dO6WgmFAarIUJIG8uHg2xhKY","old_business_type":"text_prompt_style","new_business_type":"text_generation_aistory","user_id":14,"client_type":"python_tool","old_connection_duration_minutes":2,"reason":"业务类型切换"} 
[2025-08-21 19:33:19] production.INFO: WebSocket业务类型切换完成 {"user_id":14,"client_type":"python_tool","from_business_types":["text_prompt_style"],"to_business_type":"text_generation_aistory","disconnected_sessions_count":1} 
[2025-08-21 19:33:19] production.INFO: WebSocket连接认证成功 {"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","expires_at":"2025-08-21T19:38:19+08:00"} 
[2025-08-21 19:33:40] production.INFO: WebSocket会话重连 {"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:35:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:35:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:35:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776103.781519,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:35:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":76.88,"total_stack_cleared":true} 
[2025-08-21 19:35:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:35:03+08:00"} 
[2025-08-21 19:36:41] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755776201_PF87JpSQ","user_id":14,"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","business_type":"text_generation_aistory"} 
[2025-08-21 19:37:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:37:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:37:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776223.80443,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:37:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":39.03,"total_stack_cleared":true} 
[2025-08-21 19:37:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:37:03+08:00"} 
[2025-08-21 19:39:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:39:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:39:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776343.80206,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:39:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":60.28,"total_stack_cleared":true} 
[2025-08-21 19:39:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:39:03+08:00"} 
[2025-08-21 19:41:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:41:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:41:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776463.795291,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:41:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":130.7,"total_stack_cleared":true} 
[2025-08-21 19:41:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:41:03+08:00"} 
[2025-08-21 19:43:03] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1310,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:43:03] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:43:03] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1310,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776583.776138,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:43:03] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":106.48,"total_stack_cleared":true} 
[2025-08-21 19:43:03] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:43:03+08:00"} 
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:44:53] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:44:53] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:44:53] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:44:59] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:44:59] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF"} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_iihBmkWI6jGy0nb86aZ5P7eYUi0frYvr","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:33:40+08:00"}]} 
[2025-08-21 19:44:59] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:44:59] production.INFO: WebSocket连接认证成功 {"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","expires_at":"2025-08-21T19:49:59+08:00"} 
[2025-08-21 19:45:15] production.INFO: WebSocket会话重连 {"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:46:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:46:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:46:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776813.562523,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:46:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":93.94,"total_stack_cleared":true} 
[2025-08-21 19:46:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:46:53+08:00"} 
[2025-08-21 19:48:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:48:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:48:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755776933.540208,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:48:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":122.57,"total_stack_cleared":true} 
[2025-08-21 19:48:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:48:53+08:00"} 
[2025-08-21 19:50:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:50:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:50:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777053.529765,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:50:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":35.88,"total_stack_cleared":true} 
[2025-08-21 19:50:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:50:53+08:00"} 
[2025-08-21 19:52:53] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1475,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:52:53] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:52:53] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1475,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777173.532086,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:52:53] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":79.48,"total_stack_cleared":true} 
[2025-08-21 19:52:53] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:52:53+08:00"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 19:54:07] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_B6yfMU1ktscatSbdO5iKVWy5bYEqWJPF","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T19:45:15+08:00"}]} 
[2025-08-21 19:54:07] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 19:54:07] production.INFO: WebSocket连接认证成功 {"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","expires_at":"2025-08-21T19:59:07+08:00"} 
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 19:54:13] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 19:54:13] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 19:54:13] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 19:54:27] production.INFO: WebSocket会话重连 {"session_id":"ws_CPwAEYZEcmTyUIlP52iNUQFsDBrEBoW0","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 19:56:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":658,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:56:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:56:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":658,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777373.287799,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:56:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":59.56,"total_stack_cleared":true} 
[2025-08-21 19:56:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:56:13+08:00"} 
[2025-08-21 19:58:13] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":658,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:58:13] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 19:58:13] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":658,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755777493.277529,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 19:58:13] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":53.17,"total_stack_cleared":true} 
[2025-08-21 19:58:13] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T19:58:13+08:00"} 
[2025-08-21 19:58:59] production.INFO: User offline {"user_id":14} 
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 20:04:45] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 20:04:45] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 20:04:45] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 20:04:45] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":0,"business_types_used":0,"max_business_types":14,"existing_business_types":[]} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":0,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 20:04:45] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj"} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":0,"existing_sessions":[]} 
[2025-08-21 20:04:45] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":0,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 20:04:45] production.INFO: WebSocket连接认证成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","expires_at":"2025-08-21T20:09:45+08:00"} 
[2025-08-21 20:05:01] production.INFO: WebSocket会话重连 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"fd":1,"existing_fd":null,"reason":"会话记录存在但连接已断开，允许重连"} 
[2025-08-21 20:06:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:06:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:06:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778005.813585,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:06:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":62.09,"total_stack_cleared":true} 
[2025-08-21 20:06:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:06:45+08:00"} 
[2025-08-21 20:08:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:08:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:08:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778125.816724,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:08:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":105.49,"total_stack_cleared":true} 
[2025-08-21 20:08:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:08:45+08:00"} 
[2025-08-21 20:10:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:10:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:10:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778245.818035,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:10:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":80.1,"total_stack_cleared":true} 
[2025-08-21 20:10:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:10:45+08:00"} 
[2025-08-21 20:12:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:12:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:12:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778365.8151,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:12:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":44.93,"total_stack_cleared":true} 
[2025-08-21 20:12:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:12:45+08:00"} 
[2025-08-21 20:14:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:14:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:14:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778485.79989,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:14:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":98.78,"total_stack_cleared":true} 
[2025-08-21 20:14:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:14:45+08:00"} 
[2025-08-21 20:16:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:16:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:16:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778605.823111,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:16:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":51.91,"total_stack_cleared":true} 
[2025-08-21 20:16:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:16:45+08:00"} 
[2025-08-21 20:18:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:18:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:18:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778725.818995,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:18:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":124.69,"total_stack_cleared":true} 
[2025-08-21 20:18:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:18:45+08:00"} 
[2025-08-21 20:20:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:20:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:20:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778845.818846,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:20:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":66.82,"total_stack_cleared":true} 
[2025-08-21 20:20:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:20:45+08:00"} 
[2025-08-21 20:22:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:22:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:22:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755778965.819475,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:22:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":122.89,"total_stack_cleared":true} 
[2025-08-21 20:22:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:22:45+08:00"} 
[2025-08-21 20:24:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:24:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:24:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779085.821096,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:24:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":63.66,"total_stack_cleared":true} 
[2025-08-21 20:24:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:24:45+08:00"} 
[2025-08-21 20:26:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:26:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:26:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779205.819033,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:26:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":86.56,"total_stack_cleared":true} 
[2025-08-21 20:26:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:26:45+08:00"} 
[2025-08-21 20:28:37] production.INFO: 从指定WebSocket会话获取business_type {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","business_type":"text_generation_aistory"} 
[2025-08-21 20:28:37] production.INFO: 项目验证成功 {"project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 20:28:37] production.INFO: 项目验证成功 {"business_type":"text_generation_aistory","project_id":3,"project_name":null,"user_id":14} 
[2025-08-21 20:28:37] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4077,"current_balance":"9708.13","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-21 20:28:37] production.DEBUG: TransactionManager::begin 调用 {"context":"PointsService.freezePoints","current_level_before":0,"stack_size_before":0,"process_id":16696,"memory_usage":16777216,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:37] production.DEBUG: Lumen事务开始 {"context":"PointsService.freezePoints","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:28:37] production.DEBUG: TransactionManager::commit 调用 {"context":"PointsService.freezePoints","current_level_before":1,"stack_size_before":1,"process_id":16696,"memory_usage":18874368,"transaction_stack_before":[{"context":"PointsService.freezePoints","level":1,"timestamp":1755779317.042658,"memory_usage":16777216}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\PointsService.php","line":159,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":163,"function":"freezePoints","class":"App\\Services\\PyApi\\PointsService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Http\\Controllers\\PyApi\\AiGenerationController.php","line":301,"function":"generateTextWithWebSocket","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"generateTextWithWebSocket","class":"App\\Http\\Controllers\\PyApi\\AiGenerationController","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:37] production.DEBUG: 事务提交 {"context":"PointsService.freezePoints","level":1,"action":"real_commit","duration_ms":73.11,"total_stack_cleared":true} 
[2025-08-21 20:28:37] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4077,"business_type":"text_generation","business_id":null,"transaction_id":320,"freeze_id":320} 
[2025-08-21 20:28:37] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"estimated_cost":1.4077,"generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-21 20:28:37] production.INFO: AI生成任务记录创建成功 {"task_record_id":57,"external_task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"status":"pending","created_outside_transaction":true} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"ProcessTextGeneration.handle","current_level_before":0,"stack_size_before":0,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":118,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":37,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen事务开始 {"context":"ProcessTextGeneration.handle","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:28:39] production.DEBUG: ProcessTextGeneration.handle - 事务开始 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"transaction_level":1,"in_transaction":true,"nesting_info":{"current_level":1,"max_level":30,"stack_size":1,"in_transaction":true,"is_nested":false,"remaining_capacity":29},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520}]} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","timestamp":1755779319,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 20:28:39] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","progress":10,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送开始 {"push_id":"push_68a710f7c59f6","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","data_size":239,"max_attempts":3,"timestamp":"2025-08-21T20:28:39+08:00"} 
[2025-08-21 20:28:39] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":513} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":20971520,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755779319.840672,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":3.07} 
[2025-08-21 20:28:39] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","event_type":"ai_generation_progress","data_size":239} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送成功 {"push_id":"push_68a710f7c59f6","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","attempt":1,"success_count":1,"attempt_duration_ms":34.33,"total_duration_ms":45.05,"timestamp":"2025-08-21T20:28:39+08:00"} 
[2025-08-21 20:28:39] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":10,"message":"开始文本生成任务"} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":10,"message":"开始文本生成任务","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","timestamp":1755779319,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 20:28:39] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","progress":30,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送开始 {"push_id":"push_68a710f7d15b9","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","data_size":217,"max_attempts":3,"timestamp":"2025-08-21T20:28:39+08:00"} 
[2025-08-21 20:28:39] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":491} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":20971520,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755779319.867604,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.52} 
[2025-08-21 20:28:39] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","event_type":"ai_generation_progress","data_size":217} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送成功 {"push_id":"push_68a710f7d15b9","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","attempt":1,"success_count":1,"attempt_duration_ms":11.7,"total_duration_ms":21.87,"timestamp":"2025-08-21T20:28:39+08:00"} 
[2025-08-21 20:28:39] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":30,"message":"连接AI平台"} 
[2025-08-21 20:28:39] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":30,"message":"连接AI平台","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.generateText","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":387,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.generateText","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:39] production.DEBUG: AiGenerationService.generateText - 事务开始 {"user_id":14,"external_task_id":"text_gen_1755779317_DXvViTKl","transaction_level":2,"in_transaction":true,"nesting_info":{"current_level":2,"max_level":30,"stack_size":2,"in_transaction":true,"is_nested":true,"remaining_capacity":28},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520}]} 
[2025-08-21 20:28:39] production.INFO: generateText方法 - 参数检查 {"user_id":14,"external_task_id":"text_gen_1755779317_DXvViTKl","generation_params":{"max_tokens":"1000","temperature":"0.7","top_p":0.9,"estimated_cost":1.4077},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4,"has_estimated_cost":true,"estimated_cost_value":1.4077} 
[2025-08-21 20:28:39] production.INFO: 任务状态更新为处理中 {"task_record_id":57,"external_task_id":"text_gen_1755779317_DXvViTKl","status":"processing"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1060,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:39] production.INFO: 🔍 开始执行文本生成任务 {"task_id":57,"user_id":14,"platform":"deepseek","model_name":"deepseek-chat","task_type":"text_generation_aistory","external_task_id":"text_gen_1755779317_DXvViTKl"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.start","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":20971520,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":163,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.start","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:39] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_length":513} 
[2025-08-21 20:28:39] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_type":"ai_generation_progress"} 
[2025-08-21 20:28:39] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-21 20:28:39] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.start","current_level_before":4,"stack_size_before":4,"process_id":31524,"memory_usage":20971520,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755779319.890927,"memory_usage":20971520},{"context":"AiGenerationTask.start","level":4,"timestamp":1755779319.89143,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":169,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1075,"function":"start","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:39] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.start","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":14.53} 
[2025-08-21 20:28:39] production.INFO: AI生成任务开始 {"task_id":57,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek"} 
[2025-08-21 20:28:39] production.INFO: 🔍 准备调用AI服务 {"task_id":57,"platform":"deepseek","task_type":"text_generation_aistory","request_data":{"model":"deepseek-chat","max_tokens":"1000","temperature":"0.7","top_p":0.9,"prompt_length":1593},"ai_service_url":"https://aiapi.tiptop.cn/"} 
[2025-08-21 20:28:39] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":3023} 
[2025-08-21 20:28:39] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-21 20:28:39] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_length":491} 
[2025-08-21 20:28:39] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_type":"ai_generation_progress"} 
[2025-08-21 20:28:39] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":7608} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":790,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"AiServiceClient.recordUserPreference","current_level_before":4,"stack_size_before":4,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755779319.890927,"memory_usage":20971520},{"context":"AiServiceClient.recordUserPreference","level":4,"timestamp":1755779320.041934,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":850,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\AiServiceClient.php","line":745,"function":"recordUserPreference","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1109,"function":"callWithUserChoice","class":"App\\Services\\AiServiceClient","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiServiceClient.recordUserPreference","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":12.35} 
[2025-08-21 20:28:40] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_aistory","usage_count":26,"success_rate":1.0} 
[2025-08-21 20:28:40] production.INFO: 🔍 AI服务调用完成 {"task_id":57,"platform":"deepseek","response_success":true,"response_status":"unknown","response_mode":"mock","has_data":true,"has_error":false} 
[2025-08-21 20:28:40] production.INFO: 🔍 AI服务调用成功，开始解析响应 {"task_id":57,"response_data_keys":["id","object","created","model","choices","usage"],"response_data_type":"array"} 
[2025-08-21 20:28:40] production.INFO: 🔍 AI响应解析完成 {"task_id":57,"generated_text_length":6590,"tokens_used":1849,"finish_reason":"stop","model":"deepseek-chat"} 
[2025-08-21 20:28:40] production.INFO: 🔍 开始完成任务并消费积分 {"task_id":57,"output_data_keys":["text","finish_reason","model","usage"],"tokens_used":1849} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"AiGenerationTask.complete","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":197,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"AiGenerationTask.complete","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationTask.complete","current_level_before":4,"stack_size_before":4,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755779319.890927,"memory_usage":20971520},{"context":"AiGenerationTask.complete","level":4,"timestamp":1755779320.055084,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\AiGenerationTask.php","line":210,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1157,"function":"complete","class":"App\\Models\\AiGenerationTask","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationTask.complete","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":4.65} 
[2025-08-21 20:28:40] production.INFO: AI生成任务完成 {"task_id":57,"user_id":14,"task_type":"text_generation_aistory","platform":"deepseek","tokens_used":1849,"processing_time_ms":1055} 
[2025-08-21 20:28:40] production.INFO: 🔍 任务完成 {"task_id":57,"task_status":"completed","processing_time_ms":1055} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationService.executeTextGeneration","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"AiGenerationService.executeTextGeneration","level":3,"timestamp":1755779319.890927,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1190,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":538,"function":"executeTextGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationService.executeTextGeneration","level":3,"action":"nested_commit","remaining_stack_size":2,"duration_ms":169.41} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":591,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"action":"nested_detected","framework":"Lumen 10","stack_size":3,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"ProjectService.updateProject","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectService.php","line":314,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":613,"function":"updateProject","class":"App\\Services\\PyApi\\ProjectService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"ProjectService.updateProject","level":4,"action":"nested_detected","framework":"Lumen 10","stack_size":4,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.INFO: 项目更新成功 {"project_id":3,"user_id":14,"updated_fields":["title","description","story_content","title_confirmed","project_config","status","updated_at"]} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"ProjectService.updateProject","current_level_before":4,"stack_size_before":4,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"timestamp":1755779320.063179,"memory_usage":23068672},{"context":"ProjectService.updateProject","level":4,"timestamp":1755779320.063872,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectService.php","line":337,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":613,"function":"updateProject","class":"App\\Services\\PyApi\\ProjectService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"ProjectService.updateProject","level":4,"action":"nested_commit","remaining_stack_size":3,"duration_ms":5.46} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","current_level_before":3,"stack_size_before":3,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520},{"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"timestamp":1755779320.063179,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\ProjectStoryboardService.php","line":675,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":1383,"function":"extractStoryboardsFromStory","class":"App\\Services\\PyApi\\ProjectStoryboardService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":572,"function":"handleStoryboardGeneration","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"ProjectStoryboardService.extractStoryboardsFromStory","level":3,"action":"nested_commit","remaining_stack_size":2,"duration_ms":127.74} 
[2025-08-21 20:28:40] production.INFO: 故事分镜拆解成功 {"project_id":3,"scenario_count":3,"storyboard_count":16,"character_count":4} 
[2025-08-21 20:28:40] production.INFO: 文本生成任务创建成功 {"task_id":57,"user_id":14,"model_id":1,"estimated_cost":1.4077} 
[2025-08-21 20:28:40] production.DEBUG: AiGenerationService.generateText - 准备提交事务 {"user_id":14,"external_task_id":"text_gen_1755779317_DXvViTKl","transaction_level":2,"in_transaction":true,"nesting_info":{"current_level":2,"max_level":30,"stack_size":2,"in_transaction":true,"is_nested":true,"remaining_capacity":28},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520}]} 
[2025-08-21 20:28:40] production.INFO: 任务状态更新为完成 {"task_record_id":57,"external_task_id":"text_gen_1755779317_DXvViTKl","status":"completed"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"AiGenerationService.generateText","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"AiGenerationService.generateText","level":2,"timestamp":1755779319.879909,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\AiGenerationService.php","line":684,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":145,"function":"generateText","class":"App\\Services\\PyApi\\AiGenerationService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"AiGenerationService.generateText","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":315.5} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":60,"message":"AI文本生成完成","push_type":"ai_generation_progress","timestamp":1755779320,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 20:28:40] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","progress":60,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送开始 {"push_id":"push_68a710f8306b6","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","data_size":229,"max_attempts":3,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":1,"data_size":503} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755779320.207537,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.55} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","event_type":"ai_generation_progress","data_size":229} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送成功 {"push_id":"push_68a710f8306b6","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","attempt":1,"success_count":1,"attempt_duration_ms":10.85,"total_duration_ms":21.1,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":60,"message":"AI文本生成完成"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":60,"message":"AI文本生成完成","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 20:28:40] production.DEBUG: ProcessTextGeneration Job - 任务参数 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"context":"111","estimated_cost":1.4077,"points_status":"already_frozen_in_generateTextWithWebSocket"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","timestamp":1755779320,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 20:28:40] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","progress":80,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送开始 {"push_id":"push_68a710f8364ed","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","data_size":227,"max_attempts":3,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":2,"data_size":501} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755779320.231599,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.55} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","event_type":"ai_generation_progress","data_size":227} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送成功 {"push_id":"push_68a710f8364ed","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","attempt":1,"success_count":1,"attempt_duration_ms":10.79,"total_duration_ms":21.21,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":80,"message":"保存文本数据"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":80,"message":"保存文本数据","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送进度 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress","timestamp":1755779320,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"pushProgress"} 
[2025-08-21 20:28:40] production.INFO: 进度推送使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","progress":100,"method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationProgress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送开始 {"push_id":"push_68a710f83c3f4","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","data_size":228,"max_attempts":3,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","user_id":14,"business_type":"text_generation_aistory","message_type":"ai_generation_progress","list_length":3,"data_size":502} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":23068672,"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":313,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: Lumen嵌套事务检测 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_detected","framework":"Lumen 10","stack_size":2,"parent_context":"ProcessTextGeneration.handle"} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.incrementMessageCount","current_level_before":2,"stack_size_before":2,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520},{"context":"WebSocketSession.incrementMessageCount","level":2,"timestamp":1755779320.256092,"memory_usage":23068672}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Models\\WebSocketSession.php","line":317,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":719,"function":"incrementMessageCount","class":"App\\Models\\WebSocketSession","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketService.php","line":770,"function":"pushMessage","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":1017,"function":"pushToUser","class":"App\\Services\\PyApi\\WebSocketService","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Services\\PyApi\\WebSocketEventService.php","line":131,"function":"pushWithRetry","class":"App\\Services\\PyApi\\WebSocketEventService","type":"->"}]} 
[2025-08-21 20:28:40] production.DEBUG: 发生嵌套事务提交，已规避 {"context":"WebSocketSession.incrementMessageCount","level":2,"action":"nested_commit","remaining_stack_size":1,"duration_ms":1.55} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","event_type":"ai_generation_progress","data_size":228} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送成功 {"push_id":"push_68a710f83c3f4","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755779317_DXvViTKl","attempt":1,"success_count":1,"attempt_duration_ms":10.96,"total_duration_ms":20.73,"timestamp":"2025-08-21T20:28:40+08:00"} 
[2025-08-21 20:28:40] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":100,"message":"文本生成完成"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送结果 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"progress":100,"message":"文本生成完成","push_success":"yes","push_method":"Redis通道桥接","push_message":"AI生成进度推送成功"} 
[2025-08-21 20:28:40] production.INFO: 使用传入的taskType，跳过数据库查询 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory","method":"App\\Services\\PyApi\\WebSocketEventService::pushAiGenerationCompleted"} 
[2025-08-21 20:28:40] production.ERROR: 成功情况下必须有任务记录 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"task_type":"text_generation_aistory"} 
[2025-08-21 20:28:40] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"push_success":"no","push_method":"Redis通道桥接","push_message":"成功情况下必须有任务记录","points_consumed":false} 
[2025-08-21 20:28:40] production.DEBUG: ProcessTextGeneration.handle - 准备提交事务 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"transaction_level":1,"in_transaction":true,"nesting_info":{"current_level":1,"max_level":30,"stack_size":1,"in_transaction":true,"is_nested":false,"remaining_capacity":29},"transaction_stack":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520}]} 
[2025-08-21 20:28:40] production.DEBUG: TransactionManager::commit 调用 {"context":"ProcessTextGeneration.handle","current_level_before":1,"stack_size_before":1,"process_id":31524,"memory_usage":23068672,"transaction_stack_before":[{"context":"ProcessTextGeneration.handle","level":1,"timestamp":1755779319.802844,"memory_usage":20971520}],"backtrace":[{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\app\\Jobs\\ProcessTextGeneration.php","line":204,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":36,"function":"handle","class":"App\\Jobs\\ProcessTextGeneration","type":"->"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php","line":41,"function":"Illuminate\\Container\\{closure}","class":"Illuminate\\Container\\BoundMethod","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":93,"function":"unwrapIfClosure","class":"Illuminate\\Container\\Util","type":"::"},{"file":"D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php","line":37,"function":"callBoundMethod","class":"Illuminate\\Container\\BoundMethod","type":"::"}]} 
[2025-08-21 20:28:40] production.DEBUG: 事务提交 {"context":"ProcessTextGeneration.handle","level":1,"action":"real_commit","duration_ms":479.64,"total_stack_cleared":true} 
[2025-08-21 20:28:40] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755779317_DXvViTKl","user_id":14,"context":"111","ai_task_id":57,"cost":0,"processing_steps_completed":{"1_websocket_session_validation":true,"2_progress_notifications":true,"3_ai_text_generation":true,"4_parameter_validation":true,"5_result_processing":true,"6_websocket_completion_event":false,"7_transaction_commit":true}} 
[2025-08-21 20:28:40] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_length":503} 
[2025-08-21 20:28:40] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_type":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_length":501} 
[2025-08-21 20:28:40] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_type":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","list_key":"websocket:queue:ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_length":502} 
[2025-08-21 20:28:40] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","message_type":"ai_generation_progress"} 
[2025-08-21 20:28:40] production.INFO: WebSocket消息推送成功 {"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-21 20:28:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:28:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:28:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779325.815065,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:28:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":28.85,"total_stack_cleared":true} 
[2025-08-21 20:28:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:28:45+08:00"} 
[2025-08-21 20:30:45] production.DEBUG: TransactionManager::begin 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":0,"stack_size_before":0,"process_id":1524,"memory_usage":20971520,"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":225,"function":"begin","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:30:45] production.DEBUG: Lumen事务开始 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_begin","framework":"Lumen 10","stack_size":1} 
[2025-08-21 20:30:45] production.DEBUG: TransactionManager::commit 调用 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","current_level_before":1,"stack_size_before":1,"process_id":1524,"memory_usage":20971520,"transaction_stack_before":[{"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"timestamp":1755779445.822382,"memory_usage":20971520}],"backtrace":[{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Models/WebSocketSession.php","line":238,"function":"commit","class":"App\\Services\\Common\\TransactionManager","type":"::"},{"file":"/cygdrive/d/longtool/phpStudy_64/WWW/tool_api/php/api/app/Console/Commands/WebSocketServer.php","line":279,"function":"batchUpdateHeartbeatBySessionId","class":"App\\Models\\WebSocketSession","type":"::"},{"function":"App\\Console\\Commands\\{closure}","class":"App\\Console\\Commands\\WebSocketServer","type":"->"}]} 
[2025-08-21 20:30:45] production.DEBUG: 事务提交 {"context":"WebSocketSession.batchUpdateHeartbeatBySessionId","level":1,"action":"real_commit","duration_ms":91.63,"total_stack_cleared":true} 
[2025-08-21 20:30:45] production.INFO: WebSocket按session_id批量心跳更新成功 {"total_sessions":1,"updated_sessions":1,"timestamp":"2025-08-21T20:30:45+08:00"} 
[2025-08-21 20:31:32] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-21 20:31:32] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-21 20:31:32] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-21 20:31:32] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-21 20:31:32] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-21 20:31:33] production.INFO: WebSocket连接限制检查 {"user_id":14,"client_type":"python_tool","current_connections":1,"business_types_used":1,"max_business_types":14,"existing_business_types":["text_generation_aistory"]} 
[2025-08-21 20:31:33] production.INFO: WebSocket连接限制检查通过 {"user_id":14,"client_type":"python_tool","current_connections":1,"max_connections":14,"frontend_business_type":"aistory","status":"allowed"} 
[2025-08-21 20:31:33] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"aistory","mapped_task_type":"text_generation_aistory","user_id":14,"session_id":"ws_T1S26pbalwj09bGtMKyWIbackCyT9ZcY"} 
[2025-08-21 20:31:33] production.INFO: WebSocket连接管理检查 {"user_id":14,"client_type":"python_tool","target_business_type":"text_generation_aistory","existing_sessions_count":1,"existing_sessions":[{"session_id":"ws_AuFo3RYkSGv06jAKjOyKTa8vLOV9rOlj","business_type":"text_generation_aistory","status":"connected","connected_at":"2025-08-21T20:05:01+08:00"}]} 
[2025-08-21 20:31:33] production.INFO: WebSocket多连接支持：创建新连接 {"user_id":14,"client_type":"python_tool","business_type":"text_generation_aistory","frontend_business_type":"aistory","existing_same_type_connections":1,"allow_multiple_connections":false,"reason":"直接支持同用户同业务类型多连接"} 
[2025-08-21 20:31:33] production.INFO: WebSocket连接认证成功 {"session_id":"ws_T1S26pbalwj09bGtMKyWIbackCyT9ZcY","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1","cache_key":"websocket_auth_ws_T1S26pbalwj09bGtMKyWIbackCyT9ZcY","expires_at":"2025-08-21T20:36:33+08:00"} 
